{"miniprogramRoot": "dist/", "projectname": "snapism", "description": "Snapism", "appid": "wxd24dfe40c6a36332", "setting": {"urlCheck": false, "es6": true, "enhance": false, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.8.7", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}