import { defineConfig, type UserConfigExport } from '@tarojs/cli';
import devConfig from './dev';
import prodConfig from './prod';
import path from 'path';
import { UnifiedViteWeappTailwindcssPlugin } from 'weapp-tailwindcss/vite';
import tailwindcss from '@tailwindcss/postcss';
import { loadEnv } from 'vite';
// https://taro-docs.jd.com/docs/next/config#defineconfig-辅助函数
export default defineConfig<'vite'>(async (merge, { command, mode }) => {
  const env = loadEnv(mode || 'development', process.cwd());
  const baseConfig: UserConfigExport<'vite'> = {
    projectName: 'snapism',
    date: '2025-5-8',
    designWidth: 600, // Your custom design width
    deviceRatio: {
      '*': 1, // All devices use 1:1 ratio
    },
    sourceRoot: 'src',
    outputRoot: 'dist',
    plugins: [
      '@taro-hooks/plugin-react',
      'tarojs-router-next-plugin',
      '@tarojs/plugin-html',
      '@taro-platform/axios-taro-adapter/taro-plugin',
    ],
    defineConstants: {},
    alias: {
      '@': path.resolve(__dirname, '..', 'src'),
    },
    copy: {
      patterns: [],
      options: {},
    },
    framework: 'react',
    compiler: {
      type: 'vite',
      prebundle: {
        exclude: ['tarojs-router-next'],
      },
      vitePlugins: [
        {
          name: 'postcss-config-loader-plugin',
          config(config) {
            // 加载 tailwindcss
            if (typeof config.css?.postcss === 'object') {
              config.css?.postcss.plugins?.unshift(tailwindcss());
            }
          },
        },
        UnifiedViteWeappTailwindcssPlugin({
          rem2rpx: false,
        }),
      ],
    },
    mini: {
      postcss: {
        pxtransform: {
          enable: false,
          config: {},
        },
        url: {
          enable: true,
          config: {
            limit: 1024, // 设定转换尺寸上限
          },
        },

        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
    },
    h5: {
      publicPath: '/',
      staticDirectory: 'static',
      miniCssExtractPluginOption: {
        ignoreOrder: true,
        filename: 'css/[name].[hash].css',
        chunkFilename: 'css/[name].[chunkhash].css',
      },
      postcss: {
        autoprefixer: {
          enable: true,
          config: {},
        },
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
          config: {
            namingPattern: 'module', // 转换模式，取值为 global/module
            generateScopedName: '[name]__[local]___[hash:base64:5]',
          },
        },
      },
    },
    rn: {
      appName: 'taroDemo',
      postcss: {
        cssModules: {
          enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        },
      },
    },
  };
  if (process.env.NODE_ENV === 'development') {
    // 本地开发构建配置（不混淆压缩）
    return merge(
      {
        vite: {
          define: {
            'process.env': {
              ...env,
              TARO_ENV: JSON.stringify(process.env.TARO_ENV),
            },
          },
        },
      },
      baseConfig,
      devConfig
    );
  }
  // 生产构建配置（默认开启压缩混淆等）
  return merge(
    {
      vite: {
        define: {
          'process.env': {
            ...env,
            TARO_ENV: JSON.stringify(process.env.TARO_ENV),
          },
        },
      },
    },
    baseConfig,
    prodConfig
  );
});
