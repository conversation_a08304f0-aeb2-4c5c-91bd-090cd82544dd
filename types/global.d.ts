/// <reference types="@tarojs/taro" />
/// <reference types="@taro-hooks/plugin-react" />
import "@taro-hooks/plugin-react";

declare module "*.png";
declare module "*.gif";
declare module "*.jpg";
declare module "*.jpeg";
declare module "*.svg";
declare module "*.webp";
declare module "*.css";
declare module "*.less";
declare module "*.scss";
declare module "*.sass";
declare module "*.styl";

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_ENV:
      | "weapp"
      | "swan"
      | "alipay"
      | "h5"
      | "rn"
      | "tt"
      | "quickapp"
      | "qq"
      | "jd";
  }
}

declare module "axios" {
  interface InternalAxiosRequestConfig {
    skipLoading?: boolean;
  }
  interface AxiosRequestConfig {
    skipLoading?: boolean;
  }
}
