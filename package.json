{"name": "snapism", "version": "1.0.0", "private": true, "description": "Snapism", "templateInfo": {"name": "taro-hooks@2x", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "env-cmd -f .env.dev taro build --type weapp --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "postinstall": "weapp-tw patch", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write ."}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@chakra-ui/react-use-disclosure": "^2.1.0", "@tanstack/react-query": "^5.75.5", "@taro-hooks/plugin-react": "2", "@taro-hooks/shared": "2", "@taro-platform/axios-taro-adapter": "^1.0.0-alpha.3", "@taroify/core": "^0.8.0", "@taroify/icons": "^0.8.0", "@tarojs/components": "4.0.12", "@tarojs/helper": "4.0.12", "@tarojs/plugin-framework-react": "4.0.12", "@tarojs/plugin-html": "^3.6.37", "@tarojs/plugin-platform-alipay": "4.0.12", "@tarojs/plugin-platform-h5": "4.0.12", "@tarojs/plugin-platform-jd": "4.0.12", "@tarojs/plugin-platform-qq": "4.0.12", "@tarojs/plugin-platform-swan": "4.0.12", "@tarojs/plugin-platform-tt": "4.0.12", "@tarojs/plugin-platform-weapp": "4.0.12", "@tarojs/react": "4.0.12", "@tarojs/runtime": "4.0.12", "@tarojs/shared": "4.0.12", "@tarojs/taro": "4.0.12", "abortcontroller-polyfill": "^1.7.8", "axios": "^1.9.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "jwt-decode": "^4.0.0", "lodash.get": "^4.4.2", "moment": "^2.30.1", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-hooks": "2", "taro-ui": "^3.3.0", "tarojs-router-next": "^3.4.6", "unfetch": "^5.0.0", "whatwg-fetch": "^3.6.20", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/preset-react": "^7.24.1", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/vite": "^4.1.8", "@tarojs/cli": "4.0.12", "@tarojs/vite-runner": "4.0.12", "@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "4.0.12", "dotenv": "^16.5.0", "env-cmd": "^10.1.0", "eslint": "^8.57.0", "eslint-config-taro": "4.0.12", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "react-refresh": "^0.11.0", "sass": "^1.60.0", "stylelint": "^14.4.0", "tailwindcss": "^4.1.5", "tarojs-router-next-plugin": "^3.4.6", "terser": "^5.16.8", "typescript": "^5.1.0", "vite": "^4.2.0", "weapp-tailwindcss": "^4.1.7"}}