@import 'weapp-tailwindcss';

@font-face {
  font-family: 'Pretendard Variable';
  src: url('@/assets/fonts/PretendardVariable.woff2') format('woff2');
  font-weight: 45 920;
  font-display: swap;
}

@font-face {
  font-family: 'Pretendard';
  src: url('@/assets/fonts/Pretendard-Regular.woff2') format('woff2');
  font-weight: normal;
  font-display: swap;
}

view {
  font-family: 'Pretendard Variable', 'Pretendard', sans-serif;
}

@theme {
  // Gray
  --color-gray-25: #f9fafa;
  --color-gray-50: #eff2f5;
  --color-gray-100: #dbdde1;
  --color-gray-200: #bec3ca;
  --color-gray-300: #a4a9b2;
  --color-gray-400: #8a8f99;
  --color-gray-500: #71767e;
  --color-gray-600: #595c63;
  --color-gray-700: #414548;
  --color-gray-800: #292b2e;
  --color-gray-900: #121314;

  // Red
  --color-red-50: #ffeded;
  --color-red-100: #ffd2d2;
  --color-red-200: #ffa8a8;
  --color-red-300: #ff7f7f;
  --color-red-400: #ff5a5a;
  --color-red-500: #ff4242;
  --color-red-600: #d93b3b;
  --color-red-700: #b12f2f;
  --color-red-800: #8a2424;
  --color-red-900: #641919;

  // Orange
  --color-orange-50: #fff5e5;
  --color-orange-100: #ffe5bf;
  --color-orange-200: #ffd080;
  --color-orange-300: #ffb74d;
  --color-orange-400: #ffa833;
  --color-orange-500: #ff9500;
  --color-orange-600: #db7f00;
  --color-orange-700: #b36800;
  --color-orange-800: #8c5200;
  --color-orange-900: #663b00;

  // Lime
  --color-lime-50: #f9ffd3;
  --color-lime-100: #f2ffc4;
  --color-lime-200: #e7fc9a;
  --color-lime-300: #d5f85a;
  --color-lime-400: #c3e551;
  --color-lime-500: #a4c946;
  --color-lime-600: #87a33b;
  --color-lime-700: #6b8030;
  --color-lime-800: #4d6226;
  --color-lime-900: #2f441b;

  // Green
  --color-green-50: #e6f9ec;
  --color-green-100: #bff0d0;
  --color-green-200: #79eca1;
  --color-green-300: #51e685;
  --color-green-400: #33d966;
  --color-green-500: #00cc44;
  --color-green-600: #00a63a;
  --color-green-700: #008330;
  --color-green-800: #005e26;
  --color-green-900: #003a1b;

  // Teal
  --color-teal-50: #e0fafd;
  --color-teal-100: #b3f0f5;
  --color-teal-200: #80e5ee;
  --color-teal-300: #4dd9e5;
  --color-teal-400: #00c8de;
  --color-teal-500: #00aabd;
  --color-teal-600: #008a99;
  --color-teal-700: #006b78;
  --color-teal-800: #004d56;
  --color-teal-900: #002f34;

  // Blue
  --color-blue-50: #e5f2ff;
  --color-blue-100: #cde5fe;
  --color-blue-200: #b2d9ff;
  --color-blue-300: #80bfff;
  --color-blue-400: #4da6ff;
  --color-blue-500: #0080ff;
  --color-blue-600: #006bd6;
  --color-blue-700: #014c98;
  --color-blue-800: #013365;
  --color-blue-900: #01264c;

  // Purple
  --color-purple-50: #f5ebff;
  --color-purple-100: #d9b6fb;
  --color-purple-200: #cc9efa;
  --color-purple-300: #bf86f9;
  --color-purple-400: #b26ef7;
  --color-purple-500: #983ef4;
  --color-purple-600: #8920f3;
  --color-purple-700: #6514b8;
  --color-purple-800: #3f136c;
  --color-purple-900: #260b41;

  // Pink
  --color-pink-50: #ffebfa;
  --color-pink-100: #fecdf1;
  --color-pink-200: #ffb2eb;
  --color-pink-300: #ff99e4;
  --color-pink-400: #ff7fdd;
  --color-pink-500: #ff4dd0;
  --color-pink-600: #ff4dd0;
  --color-pink-700: #e830b7;
  --color-pink-800: #6c1355;
  --color-pink-900: #3d0f31;

  // Text Colors
  --color-text-primary: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-tertiary: var(--color-gray-400);
  --color-text-quaternary: var(--color-gray-300);
  --color-text-disable: var(--color-gray-200);
  --color-text-invert: #ffffff;
  --color-text-invertAlt: var(--color-gray-100);
  --color-text-strong: #000000;

  // Background Colors
  --color-bg-normal: #ffffff;
  --color-bg-alt: var(--color-gray-50);

  // Overlay Colors
  --color-overlay-normal: rgba(0, 0, 0, 0.55);
  --color-overlay-weak: rgba(0, 0, 0, 0.35);
  --color-overlay-strong: rgba(0, 0, 0, 0.75);
}

@layer components {
  .text-display1 {
    font-size: 40px;
    line-height: 56px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-display2 {
    font-size: 32px;
    line-height: 44px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-title1 {
    font-size: 24px;
    line-height: 32px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-title2 {
    font-size: 22px;
    line-height: 28px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-head1 {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-head2 {
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .text-body1 {
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-body1long {
    font-size: 16px;
    line-height: 26px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-body2 {
    font-size: 15px;
    line-height: 22px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-body2long {
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-label1 {
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-label1long {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-label2 {
    font-size: 13px;
    line-height: 18px;
    font-weight: 500;
    color: var(--color-text-primary);
  }

  .text-label2long {
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-caption1 {
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  .text-caption2 {
    font-size: 11px;
    line-height: 14px;
    font-weight: 400;
    color: var(--color-text-primary);
  }

  // Border Components
  .border-default {
    border: 1px solid var(--color-gray-100);
  }

  .border-strong {
    border: 1px solid var(--color-gray-900);
  }

  .border-weak {
    border: 1px solid var(--color-gray-50);
  }

  .border-none {
    border: none;
  }
}
