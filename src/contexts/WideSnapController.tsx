import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import usePhotoSlot, { IPhotoSlot } from '@/hooks/photo-editor/usePhotoSlot';
import {
  EImageColor,
  EImageTool,
  EProductPhotoType,
} from '@/constants/image-editor/imageEditor';
import {
  ICreateCustomProductRequest,
  TFlipValue,
  TImageOffset,
} from '@/types/imageEditor';
import { EWideSnapLayout } from '@/constants/image-editor/wideSnap';
import {
  useCreateCustomProductForGuest,
  useCreateCustomProductForMember,
} from '@/hooks/photo-editor/useCreateCustomProduct';
import { checkLoggedIn } from '@/utils/auth';
import { EPhotoFace } from '@/constants/image-editor/photocard';
import { transformSlotData } from '@/utils/custom-product/customProduct';
import LoadingFullScreen from '@/components/common/loading/LoadingFullScreen';
import { EMode } from '@/constants/enums';
import useUpdateCustomProduct from '@/hooks/custom-product/useUpdateCustomProduct';

export type PhotoSlotId = 1 | 2 | 3 | 4 | 5 | 6;

interface PhotoSlotState {
  imageUrl: string | null;
  imageServerUrl: string | null;
  imageOffset: TImageOffset;
  imageRotation: number;
  imageFlip: {
    x: TFlipValue;
    y: TFlipValue;
  };
}

interface EditorHistoryState {
  layout: EWideSnapLayout;
  color: EImageColor;
  slots: PhotoSlotState[];
}

export interface WideSnapEditorContextType {
  isSaving: boolean;
  setSaving: Dispatch<SetStateAction<boolean>>;
  exportedImage: string;
  setExportedImage: Dispatch<SetStateAction<string>>;
  isExportWithMissingImages: boolean;
  setExportWithMissingImages: Dispatch<SetStateAction<boolean>>;
  openRemindNotAllImagesUploaded: boolean;
  setOpenRemindNotAllImagesUploaded: Dispatch<SetStateAction<boolean>>;
  previewExportedImage: string;
  setPreviewExportedImage: Dispatch<SetStateAction<string>>;
  selectedLayout: EWideSnapLayout;
  setSelectedLayout: (layout: EWideSnapLayout) => void;
  colorMode: EImageColor;
  setColorMode: (color: EImageColor) => void;
  activeSlot: PhotoSlotId | null;
  setActiveSlot: (slot: PhotoSlotId | null) => void;
  imageEditAction: EImageTool | null;
  setImageEditAction: Dispatch<SetStateAction<EImageTool | null>>;
  photoSlot1: IPhotoSlot;
  photoSlot2: IPhotoSlot;
  photoSlot3: IPhotoSlot;
  photoSlot4: IPhotoSlot;
  photoSlot5: IPhotoSlot;
  photoSlot6: IPhotoSlot;
  getPhotoSlotById: (slotId: PhotoSlotId) => IPhotoSlot;

  // History management
  saveToHistory: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  updateHistoryAndIndex: (
    stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>
  ) => void;
  resetSlotsToPrevious: () => void;
  getCurrentSlotsState: () => PhotoSlotState[];
}

const WideSnapEditorContext = createContext<
  WideSnapEditorContextType | undefined
>(undefined);

interface IWideSnapEditorProviderProps {
  initData: ICreateCustomProductRequest;
  children: ReactNode;
  mode: EMode;
  productId?: string;
}
export const WideSnapEditorProvider: React.FC<IWideSnapEditorProviderProps> = ({
  children,
  initData,
  mode,
  productId,
}) => {
  const initFaceData = initData.slots[0];
  const createCustomProductForMember = useCreateCustomProductForMember();
  const createCustomProductForGuest = useCreateCustomProductForGuest();
  const updateCustomProduct = useUpdateCustomProduct();

  const [previewExportedImage, setPreviewExportedImage] = useState('');
  const [openRemindNotAllImagesUploaded, setOpenRemindNotAllImagesUploaded] =
    useState(false);
  const [isExportWithMissingImages, setExportWithMissingImages] =
    useState(false);

  const [isSaving, setSaving] = useState(false);
  const [exportedImage, setExportedImage] = useState('');
  const [activeSlot, setActiveSlot] = useState<PhotoSlotId | null>(null);
  const [imageEditAction, setImageEditAction] = useState<EImageTool | null>(
    null
  );
  const [selectedLayout, setSelectedLayout] = useState<EWideSnapLayout>(
    initFaceData.layout as EWideSnapLayout
  );
  const [colorMode, setColorMode] = useState<EImageColor>(initFaceData.color);

  // History management state
  const [history, setHistory] = useState<EditorHistoryState[]>([]);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const initialSlotsRef = useRef<PhotoSlotState[]>([]);

  const photoSlot1 = usePhotoSlot(initFaceData?.images[0]);
  const photoSlot2 = usePhotoSlot(initFaceData?.images[1]);
  const photoSlot3 = usePhotoSlot(initFaceData?.images[2]);
  const photoSlot4 = usePhotoSlot(initFaceData?.images[3]);
  const photoSlot5 = usePhotoSlot(initFaceData?.images[4]);
  const photoSlot6 = usePhotoSlot(initFaceData?.images[5]);

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < history.length - 1;

  const photoSlots = useMemo(
    () => ({
      1: photoSlot1,
      2: photoSlot2,
      3: photoSlot3,
      4: photoSlot4,
      5: photoSlot5,
      6: photoSlot6,
    }),
    [photoSlot1, photoSlot2, photoSlot3, photoSlot4, photoSlot5, photoSlot6]
  );

  const getPhotoSlotById = useCallback(
    (slotId: PhotoSlotId): IPhotoSlot => {
      return photoSlots[slotId];
    },
    [photoSlots]
  );

  // Create current state snapshot with optional overrides
  const createStateSnapshot = useCallback(
    (overrides: Partial<EditorHistoryState> = {}): EditorHistoryState => {
      return {
        layout:
          overrides.layout !== undefined ? overrides.layout : selectedLayout,
        color: overrides.color !== undefined ? overrides.color : colorMode,
        slots:
          overrides.slots !== undefined
            ? overrides.slots
            : [
                {
                  imageUrl: photoSlot1.imageUrl,
                  imageOffset: photoSlot1.imageOffset,
                  imageRotation: photoSlot1.imageRotation,
                  imageFlip: photoSlot1.imageFlip,
                  imageServerUrl: photoSlot1.imageServerUrl,
                },
                {
                  imageUrl: photoSlot2.imageUrl,
                  imageOffset: photoSlot2.imageOffset,
                  imageRotation: photoSlot2.imageRotation,
                  imageFlip: photoSlot2.imageFlip,
                  imageServerUrl: photoSlot2.imageServerUrl,
                },
                {
                  imageUrl: photoSlot3.imageUrl,
                  imageOffset: photoSlot3.imageOffset,
                  imageRotation: photoSlot3.imageRotation,
                  imageFlip: photoSlot3.imageFlip,
                  imageServerUrl: photoSlot3.imageServerUrl,
                },
                {
                  imageUrl: photoSlot4.imageUrl,
                  imageOffset: photoSlot4.imageOffset,
                  imageRotation: photoSlot4.imageRotation,
                  imageFlip: photoSlot4.imageFlip,
                  imageServerUrl: photoSlot4.imageServerUrl,
                },
                {
                  imageUrl: photoSlot5.imageUrl,
                  imageOffset: photoSlot5.imageOffset,
                  imageRotation: photoSlot5.imageRotation,
                  imageFlip: photoSlot5.imageFlip,
                  imageServerUrl: photoSlot5.imageServerUrl,
                },
                {
                  imageUrl: photoSlot6.imageUrl,
                  imageOffset: photoSlot6.imageOffset,
                  imageRotation: photoSlot6.imageRotation,
                  imageFlip: photoSlot6.imageFlip,
                  imageServerUrl: photoSlot6.imageServerUrl,
                },
              ],
      };
    },
    [
      selectedLayout,
      colorMode,
      photoSlot1,
      photoSlot2,
      photoSlot3,
      photoSlot4,
      photoSlot5,
      photoSlot6,
    ]
  );

  // Update both history and currentIndex in a coordinated way
  const updateHistoryAndIndex = useCallback(
    (stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>) => {
      // Determine if we received a full state or just overrides
      const newState =
        'layout' in stateOrOverrides &&
        'color' in stateOrOverrides &&
        'slots' in stateOrOverrides
          ? (stateOrOverrides as EditorHistoryState)
          : createStateSnapshot(
              stateOrOverrides as Partial<EditorHistoryState>
            );

      // Calculate the new history array and index
      setHistory((prevHistory) => {
        const slicedHistory = prevHistory.slice(0, currentIndex + 1);
        slicedHistory.push(newState);

        // Limit to 10 items
        return slicedHistory.length > 10
          ? slicedHistory.slice(-10)
          : slicedHistory;
      });

      // Update the current index to point to the newly added state
      setCurrentIndex((prevIndex) => {
        if (history.length >= 10 && prevIndex === 9) {
          return 9; // Stay at max index when sliding window
        }
        return Math.min(prevIndex + 1, 9);
      });
    },
    [createStateSnapshot, currentIndex, history.length]
  );

  const saveToHistory = useCallback(() => {
    updateHistoryAndIndex({});
  }, [updateHistoryAndIndex]);

  const restoreState = useCallback(
    (state: EditorHistoryState) => {
      // Restore layout and color
      setSelectedLayout(state.layout);
      setColorMode(state.color);

      // Restore each photo slot's state
      state.slots.forEach((slot, index) => {
        const photoSlot = getPhotoSlotById((index + 1) as PhotoSlotId);
        photoSlot.setImageUrl(slot.imageUrl as string);
        photoSlot.setImageOffset(slot.imageOffset);
        photoSlot.setImageRotation(slot.imageRotation);
        photoSlot.setImageFlip(slot.imageFlip);
        photoSlot.setImageServerUrl(slot.imageServerUrl);
      });
    },
    [getPhotoSlotById]
  );

  const handleUndo = useCallback(() => {
    if (!canUndo) return;

    const newIndex = currentIndex - 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canUndo, currentIndex, history, restoreState]);

  const handleRedo = useCallback(() => {
    if (!canRedo) return;

    const newIndex = currentIndex + 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canRedo, currentIndex, history, restoreState]);

  const getCurrentSlotsState = useCallback(() => {
    return [
      {
        imageUrl: photoSlot1.imageUrl,
        imageOffset: photoSlot1.imageOffset,
        imageRotation: photoSlot1.imageRotation,
        imageFlip: photoSlot1.imageFlip,
        imageServerUrl: photoSlot1.imageServerUrl,
      },
      {
        imageUrl: photoSlot2.imageUrl,
        imageOffset: photoSlot2.imageOffset,
        imageRotation: photoSlot2.imageRotation,
        imageFlip: photoSlot2.imageFlip,
        imageServerUrl: photoSlot2.imageServerUrl,
      },
      {
        imageUrl: photoSlot3.imageUrl,
        imageOffset: photoSlot3.imageOffset,
        imageRotation: photoSlot3.imageRotation,
        imageFlip: photoSlot3.imageFlip,
        imageServerUrl: photoSlot3.imageServerUrl,
      },
      {
        imageUrl: photoSlot4.imageUrl,
        imageOffset: photoSlot4.imageOffset,
        imageRotation: photoSlot4.imageRotation,
        imageFlip: photoSlot4.imageFlip,
        imageServerUrl: photoSlot4.imageServerUrl,
      },
      {
        imageUrl: photoSlot5.imageUrl,
        imageOffset: photoSlot5.imageOffset,
        imageRotation: photoSlot5.imageRotation,
        imageFlip: photoSlot5.imageFlip,
        imageServerUrl: photoSlot5.imageServerUrl,
      },
      {
        imageUrl: photoSlot6.imageUrl,
        imageOffset: photoSlot6.imageOffset,
        imageRotation: photoSlot6.imageRotation,
        imageFlip: photoSlot6.imageFlip,
        imageServerUrl: photoSlot6.imageServerUrl,
      },
    ];
  }, [photoSlot1, photoSlot2, photoSlot3, photoSlot4, photoSlot5, photoSlot6]);

  const resetSlotsToPrevious = useCallback(() => {
    if (initialSlotsRef.current.length > 0) {
      // Restore each slot from the saved initial state
      initialSlotsRef.current.forEach((slotData, index) => {
        const photoSlot = getPhotoSlotById((index + 1) as PhotoSlotId);
        photoSlot.setImageUrl(slotData.imageUrl as string);
        photoSlot.setImageOffset(slotData.imageOffset);
        photoSlot.setImageRotation(slotData.imageRotation);
        photoSlot.setImageFlip(slotData.imageFlip);
        photoSlot.setImageServerUrl(slotData.imageServerUrl);
      });
    }
  }, [getPhotoSlotById]);

  const updateLayout = useCallback(
    (newLayout: EWideSnapLayout) => {
      setSelectedLayout(newLayout);
      updateHistoryAndIndex({ layout: newLayout });
    },
    [updateHistoryAndIndex]
  );

  const updateColor = useCallback(
    (newColor: EImageColor) => {
      setColorMode(newColor);
      updateHistoryAndIndex({ color: newColor });
    },
    [updateHistoryAndIndex]
  );

  // Handle image edit actions
  useEffect(() => {
    if (!activeSlot || !imageEditAction) {
      return;
    }

    const currentPhotoSlot = getPhotoSlotById(activeSlot);
    const isRotated = currentPhotoSlot.imageRotation % 180 !== 0;

    switch (imageEditAction) {
      case EImageTool.FLIP_VERTICAL: {
        const newFlip = isRotated
          ? {
              ...currentPhotoSlot.imageFlip,
              x: -currentPhotoSlot.imageFlip.x as TFlipValue,
            }
          : {
              ...currentPhotoSlot.imageFlip,
              y: -currentPhotoSlot.imageFlip.y as TFlipValue,
            };
        currentPhotoSlot.setImageFlip(newFlip);
        break;
      }

      case EImageTool.FLIP_HORIZONTAL: {
        const newFlip = isRotated
          ? {
              ...currentPhotoSlot.imageFlip,
              y: -currentPhotoSlot.imageFlip.y as TFlipValue,
            }
          : {
              ...currentPhotoSlot.imageFlip,
              x: -currentPhotoSlot.imageFlip.x as TFlipValue,
            };
        currentPhotoSlot.setImageFlip(newFlip);
        break;
      }

      case EImageTool.ROTATE_90DEG: {
        const prevRotation = currentPhotoSlot.imageRotation;
        const newRotation = (prevRotation - 90) % 360;
        const finalRotation = newRotation < 0 ? newRotation + 360 : newRotation;
        currentPhotoSlot.setImageRotation(finalRotation);
        break;
      }
    }

    setImageEditAction(null);
  }, [imageEditAction, activeSlot, getPhotoSlotById]);

  // Save initial state when editing begins
  useEffect(() => {
    if (activeSlot !== null && initialSlotsRef.current.length === 0) {
      initialSlotsRef.current = getCurrentSlotsState();
    } else if (activeSlot === null) {
      initialSlotsRef.current = [];
    }
  }, [activeSlot, getCurrentSlotsState]);

  // Initialize history with initial state
  useEffect(() => {
    if (!isInitialized) {
      const initialState = createStateSnapshot();
      setHistory([initialState]);
      setCurrentIndex(0);
      setIsInitialized(true);
    }
  }, [createStateSnapshot, isInitialized]);

  const contextValue = useMemo(
    () => ({
      isSaving,
      setSaving,
      exportedImage,
      setExportedImage,
      selectedLayout,
      setSelectedLayout: updateLayout,
      colorMode,
      setColorMode: updateColor,
      activeSlot,
      setActiveSlot,
      imageEditAction,
      setImageEditAction,
      photoSlot1,
      photoSlot2,
      photoSlot3,
      photoSlot4,
      photoSlot5,
      photoSlot6,
      getPhotoSlotById,
      // History management
      saveToHistory,
      handleRedo,
      handleUndo,
      canUndo,
      canRedo,
      updateHistoryAndIndex,
      resetSlotsToPrevious,
      getCurrentSlotsState,
      openRemindNotAllImagesUploaded,
      setOpenRemindNotAllImagesUploaded,
      isExportWithMissingImages,
      setExportWithMissingImages,
      previewExportedImage,
      setPreviewExportedImage,
    }),
    [
      selectedLayout,
      setSelectedLayout,
      colorMode,
      setColorMode,
      activeSlot,
      imageEditAction,
      photoSlot1,
      photoSlot2,
      photoSlot3,
      photoSlot4,
      photoSlot5,
      photoSlot6,
      isSaving,
      exportedImage,
      getPhotoSlotById,
      saveToHistory,
      handleRedo,
      handleUndo,
      canUndo,
      canRedo,
      updateHistoryAndIndex,
      resetSlotsToPrevious,
      getCurrentSlotsState,
      openRemindNotAllImagesUploaded,
      setOpenRemindNotAllImagesUploaded,
      isExportWithMissingImages,
      setExportWithMissingImages,
      previewExportedImage,
      setPreviewExportedImage,
    ]
  );

  // handle save to card
  function transformData() {
    let images = [
      transformSlotData(photoSlot1),
      transformSlotData(photoSlot2),
      transformSlotData(photoSlot3),
      transformSlotData(photoSlot4),
    ];
    if (selectedLayout === EWideSnapLayout.SIX_CUT) {
      images = images.concat([
        transformSlotData(photoSlot5),
        transformSlotData(photoSlot6),
      ]);
    }
    const requestBody: ICreateCustomProductRequest = {
      product_type: EProductPhotoType.WIDE_SNAP,
      slots: [
        {
          color: colorMode,
          export_img_url: exportedImage,
          layout: selectedLayout,
          position: EPhotoFace.FRONT,
          images,
        },
      ],
    };
    return requestBody;
  }

  useEffect(() => {
    if (exportedImage) {
      const payload = transformData();

      switch (mode) {
        case EMode.CREATE:
          if (checkLoggedIn()) {
            createCustomProductForMember.mutate(payload);
          } else {
            createCustomProductForGuest.mutate(payload);
          }
          break;

        case EMode.EDIT:
          if (productId) {
            updateCustomProduct.mutate({ id: productId, body: payload });
          }

          break;
      }
    }
  }, [exportedImage]);

  return (
    <WideSnapEditorContext.Provider value={contextValue}>
      {children}
      {isSaving && <LoadingFullScreen />}
    </WideSnapEditorContext.Provider>
  );
};

export const useWideSnapEditor = (): WideSnapEditorContextType => {
  const context = useContext(WideSnapEditorContext);

  if (context === undefined) {
    throw new Error(
      'useWideSnapEditor must be used within a WideSnapEditorProvider. ' +
        'Make sure you have wrapped your component tree with WideSnapEditorProvider.'
    );
  }

  return context;
};
