import React, {
  createContext,
  Dispatch,
  SetStateAction,
  useContext,
  useState,
} from 'react';

interface SearchProductContextValue {
  searchText: string;
  isSearchBarFocused: boolean;
  setSearchText: Dispatch<SetStateAction<string>>;
  searchProductText: string;
  setSearchProductText: Dispatch<SetStateAction<string>>;
  handleFocus: () => void;
  handleBlur: () => void;
  handleSearch: () => void;
  productData: any;
  infinityRef: any;
  onSubmit: any;
  fetchedParams: any;
  isLoading: boolean;
}

export const SearchProductContext = createContext<
  SearchProductContextValue | undefined
>(undefined);

interface SearchProductProviderProps {
  children: React.ReactNode;
}

export enum ERecentSearchType {
  IP = 'IP',
  PRODUCT = 'PRODUCT',
}

export type TRecentSearch = {
  type: ERecentSearchType;
  searchInput: string;
};

export const SearchProductProvider: React.FC<SearchProductProviderProps> = ({
  children,
}) => {
  const [searchText, setSearchText] = useState('');
  const [searchProductText, setSearchProductText] = useState('');
  const [isSearchBarFocused, setIsSearchBarFocused] = useState(false);

  const handleFocus = () => {
    setIsSearchBarFocused(true);
  };

  const handleBlur = () => {
    setIsSearchBarFocused(false);
  };

  const handleSearch = () => {
    console.log('Searching for:', searchText);
  };

  const contextValue: SearchProductContextValue = {
    searchText,
    isSearchBarFocused,
    setSearchText,
    handleFocus,
    handleBlur,
    handleSearch,
    productData: [],
    searchProductText,
    setSearchProductText,
    infinityRef: null,
    onSubmit: null,
    fetchedParams: null,
    isLoading: false,
  };

  return (
    <SearchProductContext.Provider value={contextValue}>
      {children}
    </SearchProductContext.Provider>
  );
};
export const useSearchProduct = () => {
  const context = useContext(SearchProductContext);
  if (!context) {
    throw new Error(
      'useSearchProduct must be used within a SearchProductProvider'
    );
  }
  return context;
};
