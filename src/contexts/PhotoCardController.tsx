import {
  EPhotoCardFace,
  EPhotoCardLayout,
  EPhotoFace,
} from '@/constants/image-editor/photocard';
import usePhotoSlot from '@/hooks/photo-editor/usePhotoSlot';
import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ICreateCustomProductRequest,
  TFlipValue,
  TImageFlip,
  TImageOffset,
  TPhotoCardExporting,
} from '@/types/imageEditor';
import {
  EImageColor,
  EImageTool,
  EProductPhotoType,
} from '@/constants/image-editor/imageEditor';
import LoadingFullScreen from '@/components/common/loading/LoadingFullScreen';
import {
  useCreateCustomProductForGuest,
  useCreateCustomProductForMember,
} from '@/hooks/photo-editor/useCreateCustomProduct';
import { transformSlotData } from '@/utils/custom-product/customProduct';
import { EMode } from '@/constants/enums';
import useUpdateCustomProduct from '@/hooks/custom-product/useUpdateCustomProduct';
import { checkLoggedIn } from '@/utils/auth';

// Define the structure of a photo face state for history
interface PhotoFaceState {
  layout: EPhotoCardLayout;
  color: EImageColor;
  imageUrl: string | null;
  imageServerUrl: string | null;
  imageOffset: TImageOffset;
  imageRotation: number;
  imageScale: number;
  imageFlip: TImageFlip;
}

// Define the structure of the entire editor state for history
interface EditorHistoryState {
  frontFace: PhotoFaceState;
  backFace: PhotoFaceState;
  editFace: EPhotoCardFace;
}

export interface PhotoCardEditorContextType {
  currentFace: EPhotoCardFace;
  setFace: Dispatch<SetStateAction<EPhotoCardFace>>;
  toggleFace: VoidFunction;
  selectedLayout: EPhotoCardLayout;
  setLayout: (value: EPhotoCardLayout) => void;
  colorMode: EImageColor;
  setColor: (value: EImageColor) => void;
  activeSlot: EPhotoCardFace | null;
  setActiveSlot: Dispatch<SetStateAction<EPhotoCardFace | null>>;
  imageEditAction: EImageTool | null;
  setImageEditAction: Dispatch<SetStateAction<EImageTool | null>>;
  exportCard: TPhotoCardExporting;
  setExportCard: Dispatch<SetStateAction<TPhotoCardExporting>>;
  isSaving: boolean;
  setSaving: Dispatch<SetStateAction<boolean>>;
  isExportWithMissingImages: boolean;
  setExportWithMissingImages: Dispatch<SetStateAction<boolean>>;
  openRemindNotAllImagesUploaded: boolean;
  setOpenRemindNotAllImagesUploaded: Dispatch<SetStateAction<boolean>>;
  previewExportCard: TPhotoCardExporting;
  setPreviewExportCard: Dispatch<SetStateAction<TPhotoCardExporting>>;
  // FRONT
  frontImageServerUrl: string;
  setFrontImageServerUrl: React.Dispatch<React.SetStateAction<string>>;
  selectedFrontLayout: EPhotoCardLayout;
  setFrontLayout: (layout: EPhotoCardLayout) => void;
  frontColor: EImageColor;
  setFrontColor: (color: EImageColor) => void;
  frontImage: string;
  setFrontImage: (url: string) => void;
  frontImageOffset: TImageOffset;
  setFrontImageOffset: (offset: TImageOffset) => void;
  frontImageRotation: number;
  setFrontImageRotation: (rotation: number) => void;
  frontImageScale: number;
  setFrontImageScale: (scale: number) => void;
  frontImageFlip: TImageFlip;
  setFrontImageFlip: (flip: TImageFlip) => void;
  // BACK
  backImageServerUrl: string;
  setBackImageServerUrl: React.Dispatch<React.SetStateAction<string>>;
  selectedBackLayout: EPhotoCardLayout;
  setBackLayout: (layout: EPhotoCardLayout) => void;
  backColor: EImageColor;
  setBackColor: (color: EImageColor) => void;
  backImage: string;
  setBackImage: (url: string) => void;
  backImageOffset: TImageOffset;
  setBackImageOffset: (offset: TImageOffset) => void;
  backImageRotation: number;
  setBackImageRotation: (rotation: number) => void;
  backImageScale: number;
  setBackImageScale: (scale: number) => void;
  backImageFlip: TImageFlip;
  setBackImageFlip: (flip: TImageFlip) => void;
  // History management
  saveToHistory: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  updateHistoryAndIndex: (
    stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>
  ) => void;
  getCurrentState: () => EditorHistoryState;
  resetFaceToPrevious: () => void;
}

// Create the context
const PhotoCardEditorContext = createContext<
  PhotoCardEditorContextType | undefined
>(undefined);

interface IPhotoCardEditorProviderProps {
  children: ReactNode;
  initData: ICreateCustomProductRequest;
  mode: EMode;
  productId?: string;
}
// Provider component
export const PhotoCardEditorProvider = ({
  children,
  initData,
  mode,
  productId,
}: IPhotoCardEditorProviderProps) => {
  const createCustomProductForMember = useCreateCustomProductForMember();
  const createCustomProductForGuest = useCreateCustomProductForGuest();
  const updateCustomProduct = useUpdateCustomProduct();

  const [currentFace, setCurrentFace] = useState<EPhotoCardFace>(
    EPhotoCardFace.FRONT
  );
  const [activeSlot, setActiveSlot] = useState<EPhotoCardFace | null>(null);
  const [imageEditAction, setImageEditAction] = useState<EImageTool | null>(
    null
  );
  const [exportCard, setExportCard] = useState<TPhotoCardExporting>({
    [EPhotoCardFace.FRONT]: '',
    [EPhotoCardFace.BACK]: '',
  });
  const [previewExportCard, setPreviewExportCard] =
    useState<TPhotoCardExporting>({
      [EPhotoCardFace.FRONT]: '',
      [EPhotoCardFace.BACK]: '',
    });
  const [openRemindNotAllImagesUploaded, setOpenRemindNotAllImagesUploaded] =
    useState(false);
  const [isSaving, setSaving] = useState(false);
  const [isExportWithMissingImages, setExportWithMissingImages] =
    useState(false);

  // History state
  const [history, setHistory] = useState<EditorHistoryState[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const isEditingRef = useRef<boolean>(false);
  const initialFaceStateRef = useRef<PhotoFaceState | null>(null);

  // FRONT STATE
  const initFrontFaceData = initData.slots[0];
  const [selectedFrontLayout, setSelectedFrontLayout] =
    useState<EPhotoCardLayout>(initFrontFaceData.layout as EPhotoCardLayout);
  const [frontColor, setInternalFrontColor] = useState<EImageColor>(
    initFrontFaceData.color
  );
  const frontSlot = usePhotoSlot(initFrontFaceData.images[0]);
  const {
    setImageServerUrl: setFrontImageServerUrl,
    imageServerUrl: frontImageServerUrl,
    imageUrl: frontImage,
    setImageUrl: setInternalFrontImage,
    imageOffset: frontImageOffset,
    setImageOffset: setInternalFrontImageOffset,
    imageScale: frontImageScale,
    setImageScale: setInternalFrontImageScale,
    imageRotation: frontImageRotation,
    setImageRotation: setInternalFrontImageRotation,
    imageFlip: frontImageFlip,
    setImageFlip: setInternalFrontImageFlip,
  } = frontSlot;

  // BACK STATE
  const initBackFaceData = initData.slots[1];
  const [selectedBackLayout, setSelectedBackLayout] =
    useState<EPhotoCardLayout>(initBackFaceData.layout as EPhotoCardLayout);
  const [backColor, setInternalBackColor] = useState<EImageColor>(
    initBackFaceData.color
  );
  const backSlot = usePhotoSlot(initBackFaceData.images[0]);
  const {
    setImageServerUrl: setBackImageServerUrl,
    imageServerUrl: backImageServerUrl,
    imageUrl: backImage,
    setImageUrl: setInternalBackImage,
    imageOffset: backImageOffset,
    setImageOffset: setInternalBackImageOffset,
    imageScale: backImageScale,
    setImageScale: setInternalBackImageScale,
    imageRotation: backImageRotation,
    setImageRotation: setInternalBackImageRotation,
    imageFlip: backImageFlip,
    setImageFlip: setInternalBackImageFlip,
  } = backSlot;

  // Computed properties
  const selectedLayout = useMemo(() => {
    return currentFace === EPhotoCardFace.FRONT
      ? selectedFrontLayout
      : selectedBackLayout;
  }, [currentFace, selectedFrontLayout, selectedBackLayout]);

  const colorMode = useMemo(() => {
    return currentFace === EPhotoCardFace.FRONT ? frontColor : backColor;
  }, [currentFace, frontColor, backColor]);

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < history.length - 1;

  // Create current state snapshot with optional overrides
  const createStateSnapshot = useCallback(
    (overrides: Partial<EditorHistoryState> = {}): EditorHistoryState => {
      const frontFaceState: PhotoFaceState = {
        layout: selectedFrontLayout,
        color: frontColor,
        imageUrl: frontImage,
        imageOffset: frontImageOffset,
        imageRotation: frontImageRotation,
        imageScale: frontImageScale,
        imageFlip: frontImageFlip,
        imageServerUrl: frontImageServerUrl,
      };

      const backFaceState: PhotoFaceState = {
        layout: selectedBackLayout,
        color: backColor,
        imageUrl: backImage,
        imageOffset: backImageOffset,
        imageRotation: backImageRotation,
        imageScale: backImageScale,
        imageFlip: backImageFlip,
        imageServerUrl: backImageServerUrl,
      };

      return {
        editFace: currentFace,
        frontFace:
          overrides.frontFace !== undefined
            ? overrides.frontFace
            : frontFaceState,
        backFace:
          overrides.backFace !== undefined ? overrides.backFace : backFaceState,
      };
    },
    [
      selectedFrontLayout,
      frontColor,
      frontImage,
      frontImageOffset,
      frontImageRotation,
      frontImageScale,
      frontImageFlip,
      selectedBackLayout,
      backColor,
      backImage,
      backImageOffset,
      backImageRotation,
      backImageScale,
      backImageFlip,
    ]
  );

  // Get current state (used for history and for external components)
  const getCurrentState = useCallback((): EditorHistoryState => {
    return createStateSnapshot();
  }, [createStateSnapshot]);

  // Update both history and currentIndex in a coordinated way
  const updateHistoryAndIndex = useCallback(
    (stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>) => {
      // Determine if we received a full state or just overrides
      const newState =
        'frontFace' in stateOrOverrides && 'backFace' in stateOrOverrides
          ? (stateOrOverrides as EditorHistoryState)
          : createStateSnapshot(
              stateOrOverrides as Partial<EditorHistoryState>
            );

      // Calculate the new history array and index
      setHistory((prevHistory) => {
        const slicedHistory = prevHistory.slice(0, currentIndex + 1);
        slicedHistory.push(newState);

        // Limit to 10 items
        return slicedHistory.length > 10
          ? slicedHistory.slice(-10)
          : slicedHistory;
      });

      // Update the current index to point to the newly added state
      setCurrentIndex((prevIndex) => {
        if (history.length >= 10 && prevIndex === 9) {
          return 9; // Stay at max index when sliding window
        }
        return Math.min(prevIndex + 1, history.length, 9);
      });
    },
    [createStateSnapshot, currentIndex, history.length]
  );

  // Save current state to history
  const saveToHistory = useCallback(() => {
    if (!isEditingRef.current) {
      updateHistoryAndIndex({});
    }
  }, [updateHistoryAndIndex]);

  // Restore a state from history
  const restoreState = useCallback((state: EditorHistoryState) => {
    setSelectedFrontLayout(state.frontFace.layout);
    setInternalFrontColor(state.frontFace.color);
    setInternalFrontImage(state.frontFace.imageUrl as string);
    setInternalFrontImageOffset(state.frontFace.imageOffset);
    setInternalFrontImageRotation(state.frontFace.imageRotation);
    setInternalFrontImageScale(state.frontFace.imageScale);
    setInternalFrontImageFlip(state.frontFace.imageFlip);

    // Restore back face state
    setSelectedBackLayout(state.backFace.layout);
    setInternalBackColor(state.backFace.color);
    setInternalBackImage(state.backFace.imageUrl as string);
    setInternalBackImageOffset(state.backFace.imageOffset);
    setInternalBackImageRotation(state.backFace.imageRotation);
    setInternalBackImageScale(state.backFace.imageScale);
    setInternalBackImageFlip(state.backFace.imageFlip);
    // Change to history face
    setCurrentFace(state.editFace);
  }, []);

  // Reset face to previous state (when canceling edits)
  const resetFaceToPrevious = useCallback(() => {
    if (initialFaceStateRef.current) {
      const faceState = initialFaceStateRef.current;

      if (currentFace === EPhotoCardFace.FRONT) {
        setSelectedFrontLayout(faceState.layout);
        setInternalFrontColor(faceState.color);
        setInternalFrontImage(faceState.imageUrl as string);
        setInternalFrontImageOffset(faceState.imageOffset);
        setInternalFrontImageRotation(faceState.imageRotation);
        setInternalFrontImageScale(faceState.imageScale);
        setInternalFrontImageFlip(faceState.imageFlip);
      } else {
        setSelectedBackLayout(faceState.layout);
        setInternalBackColor(faceState.color);
        setInternalBackImage(faceState.imageUrl as string);
        setInternalBackImageOffset(faceState.imageOffset);
        setInternalBackImageRotation(faceState.imageRotation);
        setInternalBackImageScale(faceState.imageScale);
        setInternalBackImageFlip(faceState.imageFlip);
      }

      initialFaceStateRef.current = null;
    }
  }, [
    currentFace,
    setInternalFrontImage,
    setInternalFrontImageOffset,
    setInternalFrontImageRotation,
    setInternalFrontImageScale,
    setInternalFrontImageFlip,
    setInternalBackImage,
    setInternalBackImageOffset,
    setInternalBackImageRotation,
    setInternalBackImageScale,
    setInternalBackImageFlip,
  ]);

  // Undo action
  const handleUndo = useCallback(() => {
    if (!canUndo) return;

    const newIndex = currentIndex - 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canUndo, currentIndex, history, restoreState]);

  // Redo action
  const handleRedo = useCallback(() => {
    if (!canRedo) return;

    const newIndex = currentIndex + 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canRedo, currentIndex, history, restoreState]);

  // Initialize history with first state
  useEffect(() => {
    if (!isInitialized) {
      const initialState = createStateSnapshot();
      setHistory([initialState]);
      setCurrentIndex(0);
      setIsInitialized(true);
    }
  }, [createStateSnapshot, isInitialized]);

  // Update isEditingRef when activeSlot changes
  useEffect(() => {
    isEditingRef.current = activeSlot !== null;

    // When editing begins, save initial state
    if (activeSlot !== null && !initialFaceStateRef.current) {
      if (currentFace === EPhotoCardFace.FRONT) {
        initialFaceStateRef.current = {
          layout: selectedFrontLayout,
          color: frontColor,
          imageUrl: frontImage,
          imageOffset: frontImageOffset,
          imageRotation: frontImageRotation,
          imageScale: frontImageScale,
          imageFlip: frontImageFlip,
          imageServerUrl: frontImageServerUrl,
        };
      } else {
        initialFaceStateRef.current = {
          layout: selectedBackLayout,
          color: backColor,
          imageUrl: backImage,
          imageOffset: backImageOffset,
          imageRotation: backImageRotation,
          imageScale: backImageScale,
          imageFlip: backImageFlip,
          imageServerUrl: backImageServerUrl,
        };
      }
    } else if (activeSlot === null && initialFaceStateRef.current) {
      // When editing is complete, save to history and clear initial state
      saveToHistory();
      initialFaceStateRef.current = null;
    }
  }, [
    activeSlot,
    currentFace,
    saveToHistory,
    selectedFrontLayout,
    frontColor,
    frontImage,
    frontImageOffset,
    frontImageRotation,
    frontImageScale,
    frontImageFlip,
    selectedBackLayout,
    backColor,
    backImage,
    backImageOffset,
    backImageRotation,
    backImageScale,
    backImageFlip,
  ]);

  const toggleFace = useCallback(() => {
    setCurrentFace((prevValue) => {
      if (prevValue === EPhotoCardFace.FRONT) {
        return EPhotoCardFace.BACK;
      }
      return EPhotoCardFace.FRONT;
    });
    setActiveSlot(null);
  }, []);

  function setLayout(newValue: EPhotoCardLayout) {
    if (currentFace === EPhotoCardFace.FRONT) {
      setFrontLayout(newValue);
    } else {
      setBackLayout(newValue);
    }
  }

  function setColor(newValue: EImageColor) {
    if (currentFace === EPhotoCardFace.FRONT) {
      setFrontColor(newValue);
    } else {
      setBackColor(newValue);
    }
  }

  // Front face setters with history updates
  const setFrontLayout = useCallback(
    (layout: EPhotoCardLayout) => {
      setSelectedFrontLayout(layout);
      if (!isEditingRef.current) {
        const currentState = getCurrentState();
        currentState.frontFace.layout = layout;
        updateHistoryAndIndex(currentState);
      }
    },
    [getCurrentState, updateHistoryAndIndex]
  );

  const setFrontColor = useCallback(
    (color: EImageColor) => {
      setInternalFrontColor(color);
      if (!isEditingRef.current) {
        const currentState = getCurrentState();
        currentState.frontFace.color = color;
        updateHistoryAndIndex(currentState);
      }
    },
    [getCurrentState, updateHistoryAndIndex]
  );

  const setFrontImage = useCallback(
    (url: string) => {
      setInternalFrontImage(url);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalFrontImage]
  );

  const setFrontImageOffset = useCallback(
    (offset: TImageOffset) => {
      setInternalFrontImageOffset(offset);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalFrontImageOffset]
  );

  const setFrontImageRotation = useCallback(
    (rotation: number) => {
      setInternalFrontImageRotation(rotation);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalFrontImageRotation]
  );

  const setFrontImageScale = useCallback(
    (scale: number) => {
      setInternalFrontImageScale(scale);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalFrontImageScale]
  );

  const setFrontImageFlip = useCallback(
    (flip: TImageFlip) => {
      setInternalFrontImageFlip(flip);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalFrontImageFlip]
  );

  // Back face setters with history updates
  const setBackLayout = useCallback(
    (layout: EPhotoCardLayout) => {
      setSelectedBackLayout(layout);
      if (!isEditingRef.current) {
        const currentState = getCurrentState();
        currentState.backFace.layout = layout;
        updateHistoryAndIndex(currentState);
      }
    },
    [getCurrentState, updateHistoryAndIndex]
  );

  const setBackColor = useCallback(
    (color: EImageColor) => {
      setInternalBackColor(color);
      if (!isEditingRef.current) {
        const currentState = getCurrentState();
        currentState.backFace.color = color;
        updateHistoryAndIndex(currentState);
      }
    },
    [getCurrentState, updateHistoryAndIndex]
  );

  const setBackImage = useCallback(
    (url: string) => {
      setInternalBackImage(url);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalBackImage]
  );

  const setBackImageOffset = useCallback(
    (offset: TImageOffset) => {
      setInternalBackImageOffset(offset);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalBackImageOffset]
  );

  const setBackImageRotation = useCallback(
    (rotation: number) => {
      setInternalBackImageRotation(rotation);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalBackImageRotation]
  );

  const setBackImageScale = useCallback(
    (scale: number) => {
      setInternalBackImageScale(scale);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalBackImageScale]
  );

  const setBackImageFlip = useCallback(
    (flip: TImageFlip) => {
      setInternalBackImageFlip(flip);
    },
    [getCurrentState, updateHistoryAndIndex, setInternalBackImageFlip]
  );

  // Handle image edit actions
  useEffect(() => {
    if (!imageEditAction) return;

    if (currentFace === EPhotoCardFace.FRONT) {
      const isRotated = frontImageRotation % 180 !== 0;
      switch (imageEditAction) {
        case EImageTool.FLIP_VERTICAL: {
          const newFlip = isRotated
            ? {
                ...frontImageFlip,
                x: -frontImageFlip.x as TFlipValue,
              }
            : {
                ...frontImageFlip,
                y: -frontImageFlip.y as TFlipValue,
              };
          setInternalFrontImageFlip(newFlip);
          break;
        }

        case EImageTool.FLIP_HORIZONTAL: {
          const newFlip = isRotated
            ? {
                ...frontImageFlip,
                y: -frontImageFlip.y as TFlipValue,
              }
            : {
                ...frontImageFlip,
                x: -frontImageFlip.x as TFlipValue,
              };
          setInternalFrontImageFlip(newFlip);
          break;
        }

        case EImageTool.ROTATE_90DEG: {
          const newRotation = (frontImageRotation - 90) % 360;
          const finalRotation =
            newRotation < 0 ? newRotation + 360 : newRotation;
          setInternalFrontImageRotation(finalRotation);
          break;
        }
      }
    } else {
      const isRotated = backImageRotation % 180 !== 0;
      switch (imageEditAction) {
        case EImageTool.FLIP_VERTICAL: {
          const newFlip = isRotated
            ? {
                ...backImageFlip,
                x: -backImageFlip.x as TFlipValue,
              }
            : {
                ...backImageFlip,
                y: -backImageFlip.y as TFlipValue,
              };
          setInternalBackImageFlip(newFlip);
          break;
        }

        case EImageTool.FLIP_HORIZONTAL: {
          const newFlip = isRotated
            ? {
                ...backImageFlip,
                y: -backImageFlip.y as TFlipValue,
              }
            : {
                ...backImageFlip,
                x: -backImageFlip.x as TFlipValue,
              };
          setInternalBackImageFlip(newFlip);
          break;
        }

        case EImageTool.ROTATE_90DEG: {
          const newRotation = (backImageRotation - 90) % 360;
          const finalRotation =
            newRotation < 0 ? newRotation + 360 : newRotation;
          setInternalBackImageRotation(finalRotation);
          break;
        }
      }
    }

    // If we're not in editing mode, save to history after the action
    if (!isEditingRef.current) {
      saveToHistory();
    }

    setImageEditAction(null);
  }, [
    imageEditAction,
    currentFace,
    frontImageFlip,
    setInternalFrontImageFlip,
    frontImageRotation,
    setInternalFrontImageRotation,
    backImageFlip,
    setInternalBackImageFlip,
    backImageRotation,
    setInternalBackImageRotation,
    saveToHistory,
  ]);

  function transformData() {
    const requestBody: ICreateCustomProductRequest = {
      product_type: EProductPhotoType.PHOTO_CARD,
      slots: [
        {
          color: frontColor,
          export_img_url: exportCard.FRONT,
          layout: selectedFrontLayout,
          position: EPhotoFace.FRONT,
          images: [transformSlotData(frontSlot)],
        },
        {
          color: backColor,
          export_img_url: exportCard.BACK,
          layout: selectedBackLayout,
          position: EPhotoFace.BACK,
          images: [transformSlotData(backSlot)],
        },
      ],
    };
    return requestBody;
  }

  useEffect(() => {
    if (exportCard.FRONT && exportCard.BACK) {
      const payload = transformData();
      switch (mode) {
        case EMode.CREATE:
          if (checkLoggedIn()) {
            createCustomProductForMember.mutate(payload);
          } else {
            createCustomProductForGuest.mutate(payload);
          }
          break;

        case EMode.EDIT:
          if (productId) {
            updateCustomProduct.mutate({ id: productId, body: payload });
          }
          break;
      }
    }
  }, [exportCard]);

  return (
    <PhotoCardEditorContext.Provider
      value={{
        currentFace,
        setFace: setCurrentFace,
        toggleFace,
        selectedLayout,
        setLayout,
        colorMode,
        setColor,
        activeSlot,
        setActiveSlot,
        imageEditAction,
        setImageEditAction,
        exportCard,
        setExportCard,
        isSaving,
        setSaving,
        openRemindNotAllImagesUploaded,
        setOpenRemindNotAllImagesUploaded,
        isExportWithMissingImages,
        setExportWithMissingImages,
        previewExportCard,
        setPreviewExportCard,
        // FRONT
        frontImageServerUrl,
        setFrontImageServerUrl,
        selectedFrontLayout,
        setFrontLayout,
        frontColor,
        setFrontColor,
        frontImage,
        setFrontImage,
        frontImageOffset,
        setFrontImageOffset,
        frontImageRotation,
        setFrontImageRotation,
        frontImageScale,
        setFrontImageScale,
        frontImageFlip,
        setFrontImageFlip,
        // BACK
        backImageServerUrl,
        setBackImageServerUrl,
        selectedBackLayout,
        setBackLayout,
        backColor,
        setBackColor,
        backImage,
        setBackImage,
        backImageOffset,
        setBackImageOffset,
        backImageRotation,
        setBackImageRotation,
        backImageScale,
        setBackImageScale,
        backImageFlip,
        setBackImageFlip,
        // History management
        saveToHistory,
        handleUndo,
        handleRedo,
        canUndo,
        canRedo,
        updateHistoryAndIndex,
        getCurrentState,
        resetFaceToPrevious,
      }}
    >
      {children}
      {isSaving && <LoadingFullScreen />}
    </PhotoCardEditorContext.Provider>
  );
};

// Hook to use the context
export const usePhotoCardEditor = (): PhotoCardEditorContextType => {
  const context = useContext(PhotoCardEditorContext);
  if (!context) {
    throw new Error(
      'usePhotoCardEditor must be used within a PhotoCardEditorProvider'
    );
  }
  return context;
};
