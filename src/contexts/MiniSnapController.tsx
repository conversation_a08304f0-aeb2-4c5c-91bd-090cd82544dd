import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import usePhotoSlot, { IPhotoSlot } from '@/hooks/photo-editor/usePhotoSlot';
import {
  EImageColor,
  EImageTool,
  EProductPhotoType,
} from '@/constants/image-editor/imageEditor';
import { EMiniSnapLayout } from '@/constants/image-editor/miniSnap';
import {
  ICreateCustomProductRequest,
  TFlipValue,
  TImageOffset,
} from '@/types/imageEditor';
import { EPhotoFace } from '@/constants/image-editor/photocard';
import { checkLoggedIn } from '@/utils/auth';
import {
  useCreateCustomProductForGuest,
  useCreateCustomProductForMember,
} from '@/hooks/photo-editor/useCreateCustomProduct';
import { transformSlotData } from '@/utils/custom-product/customProduct';
import LoadingFullScreen from '@/components/common/loading/LoadingFullScreen';
import { EMode } from '@/constants/enums';
import useUpdateCustomProduct from '@/hooks/custom-product/useUpdateCustomProduct';

export type PhotoSlotId = 1 | 2 | 3 | 4;

// Define the structure of a photo slot state for history
interface PhotoSlotState {
  imageUrl: string | null;
  imageServerUrl: string | null;
  imageOffset: TImageOffset;
  imageRotation: number;
  imageFlip: {
    x: TFlipValue;
    y: TFlipValue;
  };
}

// Define the structure of the entire editor state for history
interface EditorHistoryState {
  layout: EMiniSnapLayout;
  color: EImageColor;
  slots: PhotoSlotState[];
}

export interface MiniSnapEditorContextType {
  isSaving: boolean;
  setSaving: Dispatch<SetStateAction<boolean>>;
  exportedImage: string;
  setExportedImage: Dispatch<SetStateAction<string>>;
  isExportWithMissingImages: boolean;
  setExportWithMissingImages: Dispatch<SetStateAction<boolean>>;
  openRemindNotAllImagesUploaded: boolean;
  setOpenRemindNotAllImagesUploaded: Dispatch<SetStateAction<boolean>>;
  previewExportedImage: string;
  setPreviewExportedImage: Dispatch<SetStateAction<string>>;
  selectedLayout: EMiniSnapLayout;
  setSelectedLayout: (layout: EMiniSnapLayout) => void;
  colorMode: EImageColor;
  setColorMode: (color: EImageColor) => void;
  activeSlot: PhotoSlotId | null;
  setActiveSlot: (slot: PhotoSlotId | null) => void;
  imageEditAction: EImageTool | null;
  setImageEditAction: Dispatch<SetStateAction<EImageTool | null>>;
  photoSlot1: IPhotoSlot;
  photoSlot2: IPhotoSlot;
  photoSlot3: IPhotoSlot;
  photoSlot4: IPhotoSlot;
  getPhotoSlotById: (slotId: PhotoSlotId) => IPhotoSlot;

  // History management
  saveToHistory: () => void;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  updateHistoryAndIndex: (
    stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>
  ) => void;
  resetSlotsToPrevious: () => void;
  getCurrentSlotsState: () => PhotoSlotState[];
}

const MiniSnapEditorContext = createContext<
  MiniSnapEditorContextType | undefined
>(undefined);

interface IMiniSnapEditorProviderProps {
  initData: ICreateCustomProductRequest;
  mode: EMode;
  productId?: string;
  children: ReactNode;
}
export const MiniSnapEditorProvider: React.FC<IMiniSnapEditorProviderProps> = ({
  children,
  initData,
  mode,
  productId,
}) => {
  const initFaceData = initData.slots[0];
  const createCustomProductForMember = useCreateCustomProductForMember();
  const createCustomProductForGuest = useCreateCustomProductForGuest();
  const updateCustomProduct = useUpdateCustomProduct();

  const [previewExportedImage, setPreviewExportedImage] = useState('');
  const [openRemindNotAllImagesUploaded, setOpenRemindNotAllImagesUploaded] =
    useState(false);
  const [isExportWithMissingImages, setExportWithMissingImages] =
    useState(false);

  const [isSaving, setSaving] = useState(false);
  const [exportedImage, setExportedImage] = useState('');
  const [activeSlot, setActiveSlot] = useState<PhotoSlotId | null>(null);
  const [imageEditAction, setImageEditAction] = useState<EImageTool | null>(
    null
  );
  const [selectedLayout, setSelectedLayout] = useState<EMiniSnapLayout>(
    initFaceData.layout as EMiniSnapLayout
  );
  const [colorMode, setColorMode] = useState<EImageColor>(initFaceData.color);

  const [history, setHistory] = useState<EditorHistoryState[]>([]);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const initialSlotsRef = useRef<PhotoSlotState[]>([]);

  const photoSlot1 = usePhotoSlot(initFaceData.images[0]);
  const photoSlot2 = usePhotoSlot(initFaceData.images[1]);
  const photoSlot3 = usePhotoSlot(initFaceData.images[2]);
  const photoSlot4 = usePhotoSlot(initFaceData.images[3]);

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < history.length - 1;

  const photoSlots = useMemo(
    () => ({
      1: photoSlot1,
      2: photoSlot2,
      3: photoSlot3,
      4: photoSlot4,
    }),
    [photoSlot1, photoSlot2, photoSlot3, photoSlot4]
  );

  const getPhotoSlotById = useCallback(
    (slotId: PhotoSlotId): IPhotoSlot => {
      return photoSlots[slotId];
    },
    [photoSlots]
  );

  // Create current state snapshot with optional overrides
  const createStateSnapshot = useCallback(
    (overrides: Partial<EditorHistoryState> = {}): EditorHistoryState => {
      return {
        layout:
          overrides.layout !== undefined ? overrides.layout : selectedLayout,
        color: overrides.color !== undefined ? overrides.color : colorMode,
        slots:
          overrides.slots !== undefined
            ? overrides.slots
            : [
                {
                  imageUrl: photoSlot1.imageUrl,
                  imageOffset: photoSlot1.imageOffset,
                  imageRotation: photoSlot1.imageRotation,
                  imageFlip: photoSlot1.imageFlip,
                  imageServerUrl: photoSlot1.imageServerUrl,
                },
                {
                  imageUrl: photoSlot2.imageUrl,
                  imageOffset: photoSlot2.imageOffset,
                  imageRotation: photoSlot2.imageRotation,
                  imageFlip: photoSlot2.imageFlip,
                  imageServerUrl: photoSlot2.imageServerUrl,
                },
                {
                  imageUrl: photoSlot3.imageUrl,
                  imageOffset: photoSlot3.imageOffset,
                  imageRotation: photoSlot3.imageRotation,
                  imageFlip: photoSlot3.imageFlip,
                  imageServerUrl: photoSlot3.imageServerUrl,
                },
                {
                  imageUrl: photoSlot4.imageUrl,
                  imageOffset: photoSlot4.imageOffset,
                  imageRotation: photoSlot4.imageRotation,
                  imageFlip: photoSlot4.imageFlip,
                  imageServerUrl: photoSlot4.imageServerUrl,
                },
              ],
      };
    },
    [selectedLayout, colorMode, photoSlot1, photoSlot2, photoSlot3, photoSlot4]
  );

  // Update both history and currentIndex in a coordinated way - properly memoized
  const updateHistoryAndIndex = useCallback(
    (stateOrOverrides: EditorHistoryState | Partial<EditorHistoryState>) => {
      // Determine if we received a full state or just overrides
      const newState =
        'layout' in stateOrOverrides &&
        'color' in stateOrOverrides &&
        'slots' in stateOrOverrides
          ? (stateOrOverrides as EditorHistoryState)
          : createStateSnapshot(
              stateOrOverrides as Partial<EditorHistoryState>
            );

      // Calculate the new history array and index
      setHistory((prevHistory) => {
        const slicedHistory = prevHistory.slice(0, currentIndex + 1);
        slicedHistory.push(newState);

        // Limit to 10 items
        return slicedHistory.length > 10
          ? slicedHistory.slice(-10)
          : slicedHistory;
      });

      // Update the current index to point to the newly added state
      setCurrentIndex((prevIndex) => {
        if (history.length >= 10 && prevIndex === 9) {
          return 9; // Stay at max index when sliding window
        }
        return Math.min(prevIndex + 1, 9);
      });
    },
    [createStateSnapshot, currentIndex, history.length]
  );

  const saveToHistory = useCallback(() => {
    updateHistoryAndIndex({});
  }, [updateHistoryAndIndex]);

  const restoreState = useCallback(
    (state: EditorHistoryState) => {
      // Restore layout and color
      setSelectedLayout(state.layout);
      setColorMode(state.color);

      // Restore each photo slot's state
      state.slots.forEach((slot, index) => {
        const photoSlot = getPhotoSlotById((index + 1) as PhotoSlotId);
        photoSlot.setImageUrl(slot.imageUrl as string);
        photoSlot.setImageOffset(slot.imageOffset);
        photoSlot.setImageRotation(slot.imageRotation);
        photoSlot.setImageFlip(slot.imageFlip);
        photoSlot.setImageServerUrl(slot.imageServerUrl);
      });
    },
    [getPhotoSlotById]
  );

  const handleUndo = useCallback(() => {
    if (!canUndo) return;

    const newIndex = currentIndex - 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canUndo, currentIndex, history, restoreState]);

  const handleRedo = useCallback(() => {
    if (!canRedo) return;

    const newIndex = currentIndex + 1;
    setCurrentIndex(newIndex);
    restoreState(history[newIndex]);
  }, [canRedo, currentIndex, history, restoreState]);

  const getCurrentSlotsState = useCallback(() => {
    return [
      {
        imageUrl: photoSlot1.imageUrl,
        imageOffset: photoSlot1.imageOffset,
        imageRotation: photoSlot1.imageRotation,
        imageFlip: photoSlot1.imageFlip,
        imageServerUrl: photoSlot1.imageServerUrl,
      },
      {
        imageUrl: photoSlot2.imageUrl,
        imageOffset: photoSlot2.imageOffset,
        imageRotation: photoSlot2.imageRotation,
        imageFlip: photoSlot2.imageFlip,
        imageServerUrl: photoSlot2.imageServerUrl,
      },
      {
        imageUrl: photoSlot3.imageUrl,
        imageOffset: photoSlot3.imageOffset,
        imageRotation: photoSlot3.imageRotation,
        imageFlip: photoSlot3.imageFlip,
        imageServerUrl: photoSlot3.imageServerUrl,
      },
      {
        imageUrl: photoSlot4.imageUrl,
        imageOffset: photoSlot4.imageOffset,
        imageRotation: photoSlot4.imageRotation,
        imageFlip: photoSlot4.imageFlip,
        imageServerUrl: photoSlot4.imageServerUrl,
      },
    ];
  }, [photoSlot1, photoSlot2, photoSlot3, photoSlot4]);

  const resetSlotsToPrevious = useCallback(() => {
    if (initialSlotsRef.current.length > 0) {
      const [slot1Data, slot2Data, slot3Data, slot4Data] =
        initialSlotsRef.current;

      photoSlot1.setImageUrl(slot1Data.imageUrl as string);
      photoSlot1.setImageOffset(slot1Data.imageOffset);
      photoSlot1.setImageRotation(slot1Data.imageRotation);
      photoSlot1.setImageFlip(slot1Data.imageFlip);
      photoSlot1.setImageServerUrl(slot1Data.imageServerUrl);

      photoSlot2.setImageUrl(slot2Data.imageUrl as string);
      photoSlot2.setImageOffset(slot2Data.imageOffset);
      photoSlot2.setImageRotation(slot2Data.imageRotation);
      photoSlot2.setImageFlip(slot2Data.imageFlip);
      photoSlot2.setImageServerUrl(slot2Data.imageServerUrl);

      photoSlot3.setImageUrl(slot3Data.imageUrl as string);
      photoSlot3.setImageOffset(slot3Data.imageOffset);
      photoSlot3.setImageRotation(slot3Data.imageRotation);
      photoSlot3.setImageFlip(slot3Data.imageFlip);
      photoSlot3.setImageServerUrl(slot3Data.imageServerUrl);

      if (selectedLayout === EMiniSnapLayout.VERTICAL && slot4Data) {
        photoSlot4.setImageUrl(slot4Data.imageUrl as string);
        photoSlot4.setImageOffset(slot4Data.imageOffset);
        photoSlot4.setImageRotation(slot4Data.imageRotation);
        photoSlot4.setImageFlip(slot4Data.imageFlip);
        photoSlot4.setImageServerUrl(slot4Data.imageServerUrl);
      }
    }
  }, [photoSlot1, photoSlot2, photoSlot3, photoSlot4, selectedLayout]);

  // Combined update functions that both change state and save to history
  const updateLayout = useCallback(
    (newLayout: EMiniSnapLayout) => {
      setSelectedLayout(newLayout);
      updateHistoryAndIndex({ layout: newLayout });
    },
    [updateHistoryAndIndex]
  );

  const updateColor = useCallback(
    (newColor: EImageColor) => {
      setColorMode(newColor);
      updateHistoryAndIndex({ color: newColor });
    },
    [updateHistoryAndIndex]
  );

  useEffect(() => {
    if (!activeSlot || !imageEditAction) {
      return;
    }

    const currentPhotoSlot = getPhotoSlotById(activeSlot);
    const isRotated = currentPhotoSlot.imageRotation % 180 !== 0;

    switch (imageEditAction) {
      case EImageTool.FLIP_VERTICAL: {
        const newFlip = isRotated
          ? {
              ...currentPhotoSlot.imageFlip,
              x: -currentPhotoSlot.imageFlip.x as TFlipValue,
            }
          : {
              ...currentPhotoSlot.imageFlip,
              y: -currentPhotoSlot.imageFlip.y as TFlipValue,
            };
        currentPhotoSlot.setImageFlip(newFlip);
        break;
      }

      case EImageTool.FLIP_HORIZONTAL: {
        const newFlip = isRotated
          ? {
              ...currentPhotoSlot.imageFlip,
              y: -currentPhotoSlot.imageFlip.y as TFlipValue,
            }
          : {
              ...currentPhotoSlot.imageFlip,
              x: -currentPhotoSlot.imageFlip.x as TFlipValue,
            };
        currentPhotoSlot.setImageFlip(newFlip);
        break;
      }

      case EImageTool.ROTATE_90DEG: {
        const prevRotation = currentPhotoSlot.imageRotation;
        const newRotation = (prevRotation - 90) % 360;
        const finalRotation = newRotation < 0 ? newRotation + 360 : newRotation;
        currentPhotoSlot.setImageRotation(finalRotation);
        break;
      }
    }

    setImageEditAction(null);
  }, [imageEditAction, activeSlot, getPhotoSlotById]);

  useEffect(() => {
    if (activeSlot !== null && initialSlotsRef.current.length === 0) {
      initialSlotsRef.current = getCurrentSlotsState();
    } else if (activeSlot === null) {
      initialSlotsRef.current = [];
    }
  }, [activeSlot, getCurrentSlotsState]);

  useEffect(() => {
    if (!isInitialized) {
      const initialState = createStateSnapshot();
      setHistory([initialState]);
      setCurrentIndex(0);
      setIsInitialized(true);
    }
  }, [createStateSnapshot, isInitialized]);

  const contextValue = useMemo(
    () => ({
      isSaving,
      setSaving,
      exportedImage,
      setExportedImage,
      selectedLayout,
      setSelectedLayout: updateLayout,
      colorMode,
      setColorMode: updateColor,
      activeSlot,
      setActiveSlot,
      imageEditAction,
      setImageEditAction,
      photoSlot1,
      photoSlot2,
      photoSlot3,
      photoSlot4,
      getPhotoSlotById,
      saveToHistory,
      handleRedo,
      handleUndo,
      canUndo,
      canRedo,
      updateLayout,
      updateColor,
      updateHistoryAndIndex,
      history,
      currentIndex,
      resetSlotsToPrevious,
      getCurrentSlotsState,
      openRemindNotAllImagesUploaded,
      setOpenRemindNotAllImagesUploaded,
      isExportWithMissingImages,
      setExportWithMissingImages,
      previewExportedImage,
      setPreviewExportedImage,
    }),
    [
      selectedLayout,
      colorMode,
      activeSlot,
      imageEditAction,
      photoSlot1,
      photoSlot2,
      photoSlot3,
      photoSlot4,
      isSaving,
      exportedImage,
      getPhotoSlotById,
      saveToHistory,
      handleRedo,
      handleUndo,
      canUndo,
      canRedo,
      updateLayout,
      updateColor,
      updateHistoryAndIndex,
      resetSlotsToPrevious,
      openRemindNotAllImagesUploaded,
      setOpenRemindNotAllImagesUploaded,
      isExportWithMissingImages,
      setExportWithMissingImages,
      previewExportedImage,
      setPreviewExportedImage,
    ]
  );

  // handle save to card

  function transformData() {
    const images = [
      transformSlotData(photoSlot1),
      transformSlotData(photoSlot2),
      transformSlotData(photoSlot3),
    ];
    if (selectedLayout === EMiniSnapLayout.VERTICAL) {
      images.push(transformSlotData(photoSlot4));
    }
    const requestBody: ICreateCustomProductRequest = {
      product_type: EProductPhotoType.MINI_SNAP,
      slots: [
        {
          color: colorMode,
          export_img_url: exportedImage,
          layout: selectedLayout,
          position: EPhotoFace.FRONT,
          images,
        },
      ],
    };
    return requestBody;
  }

  useEffect(() => {
    if (exportedImage) {
      const payload = transformData();

      switch (mode) {
        case EMode.CREATE:
          if (checkLoggedIn()) {
            createCustomProductForMember.mutate(payload);
          } else {
            createCustomProductForGuest.mutate(payload);
          }
          break;

        case EMode.EDIT:
          if (productId) {
            updateCustomProduct.mutate({ id: productId, body: payload });
          }

          break;
      }
    }
  }, [exportedImage]);

  return (
    <MiniSnapEditorContext.Provider value={contextValue}>
      {children}
      {isSaving && <LoadingFullScreen />}
    </MiniSnapEditorContext.Provider>
  );
};

export const useMiniSnapEditor = (): MiniSnapEditorContextType => {
  const context = useContext(MiniSnapEditorContext);

  if (context === undefined) {
    throw new Error(
      'useMiniSnapEditor must be used within a MiniSnapEditorProvider. ' +
        'Make sure you have wrapped your component tree with MiniSnapEditorProvider.'
    );
  }

  return context;
};
