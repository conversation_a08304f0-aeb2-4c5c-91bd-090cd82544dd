export const FORM_COMMON_MESSAGE = {
  EMPTY_DATA: '데이터가 없습니다.', // Empty data,
  CANCEL: '취소',
  CONFIRM: '확인',
  SAVE: '저장하기',
  EDIT: '수정하기',
  REGISTER: '등록하기',
  BACK: '뒤로가기',
  YES: '예',
  NO: '아니오',
  DELETE: '삭제하기',
  GO_BACK: '돌아가기',

  FETCHING_FAILED: '가가져오기 실패했습니다.', // Fetching failed.
  CREATING_FAILED: '생성에 실패했습니다.', // Creating failed.
  CREATED_SUCCESSFULLY: '생성되었습니다.', // Created successfully.
  UPDATING_FAILED: '수정 실패했습니다.', // Updating failed.
  UPDATED_SUCCESSFULLY: '수정되었습니다.', // Updated successfully.
  DELETING_FAILED: '삭제 실패했습니다.', // Deleting failed.
  DELETED_SUCCESSFULLY: '삭제되었습니다.', // Deleted successfully.
  DELETE_CONFIRMATION: '삭제하시겠습니까?', // Are you sure to delete it?
  CANCEL_CONFIRMATION: '작업을 취소하시겠습니까?', // Are you sure you want to cancel the operation?
  CANCEL_WARNING: '취소를 누르면 모든 입력이 지워집니다.', // Pressing Cancel will clear all entries.

  API_ERROR:
    '예기치 않은 오류가 발생했습니다. 어드민에게 문의하시고 다시 시도해주세요.', // An unexpected error occurred. Please contact admin and try again later.
  DUPLICATED_ERROR: '유사한 정보가 이미 존재합니다.', // Similar information already exists.
  VALIDATE_ERROR: '올바른 형식으로 정보를 입력해 주세요.', // Please enter information in the correct format.
  IMAGE_UPLOAD_ERROR: '이미지 업로드에 실패했습니다. 다시 시도해 주세요.', // Images upload failed. Please try again

  REQUIRED: '이 정보는 필수입니다.', // This information is required.
  INPUT_PLACEHOLDER: '여기에 정보를 입력해주세요.', // Please enter the information here.
  SELECT_PLACEHOLDER: '선택', // Select.
  RELEASE_DATE: '출시일은 종료일 이전이어야 합니다.', // The release date must be before the end date.
  END_DATE: '종료일은 출시일 이후여야 합니다.', // The end date must be after the release date.
  USERNAME: '공백, 특수 문자, 이모지는 사용할 수 없습니다', // Spaces, special characters, and emojis are not allowed
  FILE_SIZE: '파일 크기가 최대 한도인 ${maxFileSizeMB}MB를 초과했습니다.', // File size exceeds the maximum limit of ${maxFileSizeMB}MB.
  FILE_TYPE:
    '파일 형식이 잘못되었습니다. 지원되는 형식은 ${supportedFormats}입니다.', // Invalid file type. Only supported formats are: ${supportedFormats}.
};

export enum RESPONSE_ERROR {
  OTP_EXCEED_MAX = 'You have exceeded the maximum allowed resend attempts 5 per day.',
  COUPON_NOT_ELIGIBLE_OR_EXPIRED_OR_DELETED = 'COUPON_NOT_ELIGIBLE_OR_EXPIRED_OR_DELETED',
  PRODUCT_SALE_END = 'Product sale end.',
}

export const RESPONSE_KO: Record<string, string> = {
  [RESPONSE_ERROR.OTP_EXCEED_MAX]: '24시간 뒤에 다시 인증해 주세요.',
  [RESPONSE_ERROR.COUPON_NOT_ELIGIBLE_OR_EXPIRED_OR_DELETED]:
    '쿠폰을 사용하지 못했어요. 다시 시도해 주세요.',
  [RESPONSE_ERROR.PRODUCT_SALE_END]: '제품 판매 종료.',
};
