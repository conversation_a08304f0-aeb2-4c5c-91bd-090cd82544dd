import { EOrder, ESortProduct } from './enums';

export const EVENT_TAB_NAME = '이벤트';

export const QUERY_KEY_TYPE = {
  LIST: 'LIST',
  DETAIL: 'DETAIL',
  GET_ALL: 'GET_ALL',
};

export const DEFAULT_PAGE_SIZE = '20';
export const DEFAULT_PAGE_NUMBER = 0;

export const REFETCH_BOTTOM_OFFSET_ROWS = 5;

export const IMAGE_CONSTRAINTS = {
  maxSize5MB: 5 * 1024 * 1024,
  pngFormats: ['.png'],
};

export const TOAST_ID = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  DELETE_CART: 'delete_cart',
};

export const ORDERS = [
  { label: '최신순', value: EOrder.DESC },
  { label: '과거순', value: EOrder.ASC },
];

export const sortProductOptions = [
  { value: ESortProduct.PRODUCT_RELEASE_DATE, label: '추천순' },
  { value: ESortProduct.PRODUCT_ID, label: '신상품순' },
  { value: ESortProduct.SALE_VOLUME, label: '판매량순' },
];
