export enum EGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}
export enum EGenderLabel {
  MALE = '남성',
  FEMALE = '여성',
  OTHER = '기타',
}

export enum EOrder {
  DESC = 'DESC',
  ASC = 'ASC',
}

export enum ProductType {
  ALL = 'ALL',
  PHOTOCARD = 'PHOTOCARD',
  MINI_SNAP = 'MINI_SNAP',
  WIDE_SNAP = 'WIDE_SNAP',
}

export enum ProductImageType {
  PHOTO_CARD_FRONT = 'PHOTO_CARD_FRONT',
  PHOTO_CARD_BACK = 'PHOTO_CARD_BACK',
  MINI_SNAP = 'MINI_SNAP',
  WIDE_SNAP = 'WIDE_SNAP',
}

export enum ESortProduct {
  PRODUCT_ID = 'PRODUCT_ID',
  SALE_VOLUME = 'SALE_VOLUME',
  PRODUCT_RELEASE_DATE = 'PRODUCT_RELEASE_DATE',
}

export enum EMode {
  VIEW = 'view',
  CREATE = 'create',
  EDIT = 'edit',
}
