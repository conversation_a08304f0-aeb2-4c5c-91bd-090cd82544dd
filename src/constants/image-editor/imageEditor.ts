import {
  ICustomProductCacheImageSlot,
  TImageFlip,
  TImageOffset,
} from '@/types/imageEditor';
import { Option, OptionIcon } from '@/types/filter';
import IconChangeImage from '@/assets/icons/icon-change-image.svg';
import IconRotate90Deg from '@/assets/icons/icon-rotate-90deg.svg';
import IconFlipHorizontal from '@/assets/icons/icon-flip-horizontal.svg';
import IconFlipVertical from '@/assets/icons/icon-flip-vertical.svg';

export const DEFAULT_OFFSET: TImageOffset = {
  x: 0,
  y: 0,
};

export const DEFAULT_FLIP: TImageFlip = {
  x: 1,
  y: 1,
};

export const DEFAULT_ROTATION = 0;
export const DEFAULT_SCALE = 1;

export enum EProductPhotoType {
  PHOTO_CARD = 'PHOTO_CARD',
  MINI_SNAP = 'BASIC_FRAME',
  WIDE_SNAP = 'WIDE_FRAME',
}

export enum EEditType {
  PRODUCT = 'PRODUCT',
  IMAGE = 'IMAGE',
}

export enum EProductTool {
  CHANGE_LAYOUT = 'CHANGE_LAYOUT',
  CHANGE_COLOR = 'CHANGE_COLOR',
}

export enum EImageTool {
  CHANGE_PHOTO = 'CHANGE_PHOTO',
  ROTATE_90DEG = 'ROTATE_90DEG',
  FLIP_HORIZONTAL = 'FLIP_HORIZONTAL',
  FLIP_VERTICAL = 'FLIP_VERTICAL',
}

export const productToolOptions: { label: string; value: EProductTool }[] = [
  {
    label: '레이아웃',
    value: EProductTool.CHANGE_LAYOUT,
  },
  {
    label: '색상',
    value: EProductTool.CHANGE_COLOR,
  },
];

export const imageToolOptions: (OptionIcon & { label: string })[] = [
  {
    icon: IconChangeImage,
    label: '사진 바꾸기',
    value: EImageTool.CHANGE_PHOTO,
  },
  {
    icon: IconRotate90Deg,
    label: '90º 회전',
    value: EImageTool.ROTATE_90DEG,
  },
  {
    icon: IconFlipHorizontal,
    label: '좌우 반전',
    value: EImageTool.FLIP_HORIZONTAL,
  },
  {
    icon: IconFlipVertical,
    label: '상하 반전',
    value: EImageTool.FLIP_VERTICAL,
  },
];

export const FILE_ACCEPT_TYPES = 'image/png, image/jpeg';

export enum EImageColor {
  BLACK = 'BLACK',
  DARK = 'DARK',
  WHITE = 'WHITE',
  GRAY = 'GRAY',
  YELLOW = 'YELLOW',
  PINK = 'PINK',
}

export const COMMON_COLOR_OPTIONS: Option[] = [
  {
    label: EImageColor.BLACK,
    value: '#282525',
  },
  {
    label: EImageColor.WHITE,
    value: '#FFFFFF',
  },
  {
    label: EImageColor.GRAY,
    value: '#BFC0C0',
  },
  {
    label: EImageColor.YELLOW,
    value: '#D3DA4B',
  },
  {
    label: EImageColor.PINK,
    value: '#E57BAD',
  },
];

export const initPhotoSlot: ICustomProductCacheImageSlot = {
  img_flip_x: DEFAULT_FLIP.x,
  img_flip_y: DEFAULT_FLIP.y,
  img_offset_x: DEFAULT_OFFSET.x,
  img_offset_y: DEFAULT_OFFSET.y,
  img_rotation: DEFAULT_ROTATION,
  img_scale: DEFAULT_SCALE,
  raw_img_element_url: '',
};
