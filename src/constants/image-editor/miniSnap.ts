import { OptionIcon } from '@/types/filter';
import IconVerticalLayout from '@/assets/icons/icon-vertical-layout.svg';
import IconHorizontalLayout from '@/assets/icons/icon-horizontal-layout.svg';

export enum EMiniSnapLayout {
  VERTICAL = 'VERTICAL',
  HORIZONTAL = 'HORIZONTAL',
}

export const MINI_SNAP_LAYOUT_OPTIONS: OptionIcon[] = [
  {
    icon: IconHorizontalLayout,
    value: EMiniSnapLayout.HORIZONTAL,
  },
  {
    icon: IconVerticalLayout,
    value: EMiniSnapLayout.VERTICAL,
  },
];

export const miniSnapInputLocation = {
  [EMiniSnapLayout.HORIZONTAL]: [
    {
      top: '50%',
      left: '17.5%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '50%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '50%',
      left: '83%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '87.5%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'none',
    },
  ],
  [EMiniSnapLayout.VERTICAL]: [
    {
      top: '20%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '40%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '62.5%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
    {
      top: '80%',
      left: '50%',
      bottom: 'unset',
      transform: 'translate(-50%, -50%)',
      display: 'block',
    },
  ],
};
