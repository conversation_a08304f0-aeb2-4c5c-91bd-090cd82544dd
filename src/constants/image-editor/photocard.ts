import { Option, OptionIcon } from '@/types/filter';
import IconPhotoCardDefault from '@/assets/icons/icon-photocard-default.svg';
import IconPhotoCardFrame from '@/assets/icons/icon-photocard-frame.svg';
import IconPhotoCardFull from '@/assets/icons/icon-photocard-full.svg';
import { EImageColor } from './imageEditor';

export const PHOTO_CARD_SIZE = {
  width: 1328,
  hight: 2072,
};

export enum EPhotoCardFace {
  FRONT = 'FRONT',
  BACK = 'BACK',
}
export import EPhotoFace = EPhotoCardFace;

export enum EPhotoCardLayout {
  DEFAULT = 'DEFAULT',
  FRAME = 'FRAME',
  FULL = 'FULL',
}

export const PHOTO_CARD_DEFAULT_COLOR_OPTIONS: Option[] = [
  {
    label: EImageColor.BLACK,
    value: '#4C4A49',
  },
  {
    label: EImageColor.WHITE,
    value: '#FFFFFF',
  },
  {
    label: EImageColor.GRAY,
    value: '#BFC0C0',
  },
  {
    label: EImageColor.DARK,
    value: '#949595',
  },
  {
    label: EImageColor.YELLOW,
    value: '#D3DA4B',
  },
  {
    label: EImageColor.PINK,
    value: '#E57BAD',
  },
];

export const PHOTO_CARD_COMMON_COLOR_OPTIONS: Option[] = [
  {
    label: EImageColor.BLACK,
    value: '#282525',
  },
  {
    label: EImageColor.WHITE,
    value: '#FFFFFF',
  },
  {
    label: EImageColor.DARK,
    value: '#949595',
  },
  {
    label: EImageColor.GRAY,
    value: '#BFC0C0',
  },
  {
    label: EImageColor.YELLOW,
    value: '#D3DA4B',
  },
  {
    label: EImageColor.PINK,
    value: '#E57BAD',
  },
];

export const PHOTO_CARD_LAYOUT_OPTIONS: OptionIcon[] = [
  {
    icon: IconPhotoCardDefault,
    value: EPhotoCardLayout.DEFAULT,
  },
  {
    icon: IconPhotoCardFrame,
    value: EPhotoCardLayout.FRAME,
  },
  {
    icon: IconPhotoCardFull,
    value: EPhotoCardLayout.FULL,
  },
];
