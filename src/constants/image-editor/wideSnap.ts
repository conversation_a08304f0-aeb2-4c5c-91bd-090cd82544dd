import { OptionIcon } from '@/types/filter';
import IconFourCutsLayout from '@/assets/icons/icon-four-cuts-layout.svg';
import IconSixCutsLayout from '@/assets/icons/icon-six-cuts-layout.svg';
import IconFourCutsBLayout from '@/assets/icons/icon-four-cutsB-layout.svg';

export enum EWideSnapLayout {
  FOUR_CUT_A = 'FOUR_CUT_A',
  FOUR_CUT_B = 'FOUR_CUT_B',
  SIX_CUT = 'SIX_CUT',
}

export const WIDE_SNAP_LAYOUT_OPTIONS: OptionIcon[] = [
  {
    icon: IconFourCutsLayout,
    value: EWideSnapLayout.FOUR_CUT_A,
  },
  {
    icon: IconFourCutsBLayout,
    value: EWideSnapLayout.FOUR_CUT_B,
  },
  {
    icon: IconSixCutsLayout,
    value: EWideSnapLayout.SIX_CUT,
  },
];

export const wideSnapInputLocation = {
  [EWideSnapLayout.FOUR_CUT_A]: [
    {
      top: '25%',
      left: 'unset',
      right: '50%',
      bottom: 'unset',
      transform: 'translate(-70%, -25%)',
      display: 'block',
    },
    {
      top: '25%',
      left: '50%',
      right: 'unset',
      bottom: 'unset',
      transform: 'translate(70%, -25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '50%',
      bottom: '25%',
      transform: 'translate(-70%, 25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: '50%',
      right: 'unset',
      bottom: '25%',
      transform: 'translate(70%, 25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '25%',
      bottom: '25%',
      // transform: 'translate(25%, -25%)',
      display: 'none',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '25%',
      bottom: '25%',
      // transform: 'translate(-50%, -50%)',
      display: 'none',
    },
  ],
  [EWideSnapLayout.FOUR_CUT_B]: [
    {
      top: '24%',
      left: 'unset',
      right: '50%',
      bottom: 'unset',
      transform: 'translate(-70%, -25%)',
      display: 'block',
    },
    {
      top: '27%',
      left: '50%',
      right: 'unset',
      bottom: 'unset',
      transform: 'translate(70%, -25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '50%',
      bottom: '28%',
      transform: 'translate(-70%, 25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: '50%',
      right: 'unset',
      bottom: '23%',
      transform: 'translate(70%, 25%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '25%',
      bottom: '25%',
      // transform: 'translate(25%, -25%)',
      display: 'none',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '25%',
      bottom: '25%',
      // transform: 'translate(-50%, -50%)',
      display: 'none',
    },
  ],
  [EWideSnapLayout.SIX_CUT]: [
    {
      top: '22%',
      left: 'unset',
      right: '50%',
      bottom: 'unset',
      transform: 'translate(-20%, -50%)',
      display: 'block',
    },
    {
      top: '23%',
      left: '50%',
      right: 'unset',
      bottom: 'unset',
      transform: 'translate(70%, -50%)',
      display: 'block',
    },
    {
      top: '50%',
      left: 'unset',
      right: '50%',
      bottom: 'unset',
      transform: 'translate(-70%, -30%)',
      display: 'block',
    },
    {
      top: '50%',
      left: '50%',
      right: 'unset',
      bottom: 'unset',
      transform: 'translate(70%, -30%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: 'unset',
      right: '50%',
      bottom: '22%',
      transform: 'translate(-70%, 50%)',
      display: 'block',
    },
    {
      top: 'unset',
      left: '50%',
      right: 'unset',
      bottom: '22%',
      transform: 'translate(70%, 50%)',
      display: 'block',
    },
  ],
};
