import { QUERY_KEY_TYPE } from './common';

interface QueryKeyConfig<T extends string | number> {
  readonly all: readonly [string];
  readonly getAll: readonly [string, string];
  list: (
    filters: object
  ) => readonly [string, typeof QUERY_KEY_TYPE.LIST, object];
  detail: (id: T) => readonly [string, typeof QUERY_KEY_TYPE.DETAIL, T];
}

export function createQueryKeys<T extends string | number>(
  baseName: string
): QueryKeyConfig<T> {
  return {
    all: [baseName] as const,
    getAll: [baseName, QUERY_KEY_TYPE.GET_ALL] as const,
    list: (filters: object) =>
      [baseName, QUERY_KEY_TYPE.LIST, filters] as const,
    detail: (id: T) => [baseName, QUERY_KEY_TYPE.DETAIL, id] as const,
  };
}

export const promotionManagementKeys = createQueryKeys<string>(
  'promotion-management'
);

export const IpManagementKeys = createQueryKeys<number>('ip-management');
export const UserStampKeys = createQueryKeys<number>('user-stamps');
export const CollectionManagementKeys = createQueryKeys<number>(
  'collection-management'
);
export const ProductManagementKeys =
  createQueryKeys<number>('product-management');

export const getMeKeys = createQueryKeys<number>('account.getMe');
export const HomeTabsKeys = createQueryKeys<number>('home-tabs');
export const notificationsKeys = createQueryKeys<number>('notifications-page');
export const eventKeys = createQueryKeys<number>('eventKey');
export const categoryOfProductsKeys = createQueryKeys('categoryOfProducts');
export const productConditionKeys = createQueryKeys('productCondition');
export const ordersKeys = createQueryKeys<number>('ordersKeys');
export const categoriesKeys = createQueryKeys('categories-page');
export const productsKeys = createQueryKeys<number>('products');
export const productsByFrameIdKeys = createQueryKeys<number>(
  'productsByFrameIdKeys'
);
export const productsByCollectionIdKeys = createQueryKeys<number>(
  'productsByCollectionIdKeys'
);
export const storesKeys = createQueryKeys<number>('storesKeys');

export const cartKeys = createQueryKeys('cart');
export const cartCouponKeys = createQueryKeys('cart-coupon');
export const frameRecommendKeys = createQueryKeys('frame-recommend');
export const frameFollowKeys = createQueryKeys('frame-follow');
export const productLikeKeys = createQueryKeys('productLikeKeys');
export const customProductKeys = createQueryKeys<string | number>(
  'custom-product'
);
export const userNotificationSettingKeys = createQueryKeys(
  'user-notification-setting'
);
export const couponListKeys = createQueryKeys<string>('coupon-list');
