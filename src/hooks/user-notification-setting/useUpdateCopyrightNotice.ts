import { useMutation, useQueryClient } from '@tanstack/react-query';
import { userNotificationSettingKeys } from '@/constants/queryKey';
import { apiClient } from '@/utils/api';

export default function useUpdateCopyrightNotice() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (copyrightNoticeEnable: boolean = true) => {
      return apiClient.post(
        `/api/user-notification-setting/copyright?copyrightNoticeEnable=${copyrightNoticeEnable}`
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userNotificationSettingKeys.all,
      });
    },
  });
}
