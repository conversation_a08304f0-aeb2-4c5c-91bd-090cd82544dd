import { useQuery } from '@tanstack/react-query';
import { userNotificationSettingKeys } from '@/constants/queryKey';
import { IGetCustomProduct } from '@/types/customProduct';
import { apiClient } from '@/utils/api';
import get from 'lodash.get';
import { checkLoggedIn } from '@/utils/auth';

export default function useGetUserNotificationSetting() {
  const { data, isError, isSuccess, isLoading, isPending } =
    useQuery<IGetCustomProduct>({
      queryKey: userNotificationSettingKeys.all,
      queryFn: async () => {
        const response = await apiClient.get(`/api/user-notification-setting`, {
          skipLoading: true,
        });
        return get(response, 'data.user_notice_copyright_enable', true);
      },
      enabled: checkLoggedIn(),
    });
  return {
    userNoticeCopyrightEnable: data,
    isError,
    isSuccess,
    isLoading,
    isPending,
  };
}
