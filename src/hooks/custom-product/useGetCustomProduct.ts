import { useQuery } from '@tanstack/react-query';
import { customProductKeys } from 'constants/queryKey';
import { IGetCustomProduct } from 'types/customProduct';
import { apiClient } from 'utils/api';

export default function useGetCustomProduct(productId: string | undefined) {
  const { data, isError, isSuccess, isLoading, isPending } =
    useQuery<IGetCustomProduct>({
      queryKey: customProductKeys.detail(productId as string),
      queryFn: async () => {
        const response = await apiClient.get(
          `/api/custom-product/${productId}`
        );
        return response.data;
      },
      enabled: !!productId,
    });
  return { data, isError, isSuccess, isLoading, isPending };
}
