import { useMutation, useQueryClient } from '@tanstack/react-query';
import { cartKeys, customProductKeys, ordersKeys } from '@/constants/queryKey';
import { ICreateCustomProductRequest } from '@/types/imageEditor';
import { apiClient } from '@/utils/api';

export default function useUpdateCustomProduct() {
  // const router = useRouter();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: {
      id: number | string;
      body: ICreateCustomProductRequest;
    }) => {
      return apiClient.put(`/api/custom-product/${payload.id}`, payload.body);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ordersKeys.all,
      });
      queryClient.invalidateQueries({
        queryKey: cartKeys.getAll,
      });
      queryClient.invalidateQueries({
        queryKey: customProductKeys.all,
      });
      setTimeout(() => {
        // router.push(`${routerPages.cart}?from=custom-product`);
      }, 100);
    },
  });
}
