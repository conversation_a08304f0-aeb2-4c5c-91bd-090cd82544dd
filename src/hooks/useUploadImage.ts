import { apiClient } from '@/utils/api';
import { useMutation } from '@tanstack/react-query';
import { AxiosResponse } from 'axios';
import get from 'lodash/get';

export default function useUploadImage() {
  return useMutation({
    mutationFn: (payload: FormData) => {
      return apiClient.post(`/api/upload/image`, payload, {
        skipLoading: true,
      });
    },
    onError: (error: AxiosResponse) => {
      if (get(error, 'code') === 'ERR_NETWORK') {
        // toaster.error({
        //   title: '네트워크 연결 후 다시 시도해 주세요.',
        // });
      } else {
        // toaster.error({
        //   title: FORM_COMMON_MESSAGE.IMAGE_UPLOAD_ERROR,
        // });
      }
    },
  });
}
