import {
  DEFAULT_FLIP,
  DEFAULT_OFFSET,
  DEFAULT_ROTATION,
  DEFAULT_SCALE,
} from '@/constants/image-editor/imageEditor';
import React, { useState } from 'react';
import {
  ICustomProductCacheImageSlot,
  TImageFlip,
  TImageOffset,
} from '@/types/imageEditor';

export interface IPhotoSlot {
  imageUrl: string;
  setImageUrl: React.Dispatch<React.SetStateAction<string>>;
  imageServerUrl: string;
  setImageServerUrl: React.Dispatch<React.SetStateAction<string>>;
  imageOffset: TImageOffset;
  setImageOffset: React.Dispatch<React.SetStateAction<TImageOffset>>;
  imageRotation: number;
  setImageRotation: React.Dispatch<React.SetStateAction<number>>;
  imageScale: number;
  setImageScale: React.Dispatch<React.SetStateAction<number>>;
  imageFlip: TImageFlip;
  setImageFlip: React.Dispatch<React.SetStateAction<TImageFlip>>;
}

export default function usePhotoSlot(
  init: ICustomProductCacheImageSlot | undefined
) {
  const [imageUrl, setImageUrl] = useState(init?.raw_img_element_url || '');
  const [imageServerUrl, setImageServerUrl] = useState(
    init?.raw_img_element_url || ''
  );
  const [imageOffset, setImageOffset] = useState({
    x: init?.img_offset_x || DEFAULT_OFFSET.x,
    y: init?.img_offset_y || DEFAULT_OFFSET.y,
  });
  const [imageRotation, setImageRotation] = useState(
    init?.img_rotation || DEFAULT_ROTATION
  );
  const [imageScale, setImageScale] = useState(
    init?.img_scale || DEFAULT_SCALE
  );
  const [imageFlip, setImageFlip] = useState({
    x: init?.img_flip_x || DEFAULT_FLIP.x,
    y: init?.img_flip_y || DEFAULT_FLIP.y,
  });

  return {
    imageUrl,
    setImageUrl,
    imageServerUrl,
    setImageServerUrl,
    imageOffset,
    setImageOffset,
    imageRotation,
    setImageRotation,
    imageScale,
    setImageScale,
    imageFlip,
    setImageFlip,
  };
}
