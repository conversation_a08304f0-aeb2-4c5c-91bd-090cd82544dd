import { EProductPhotoType } from 'constants/image-editor/imageEditor';
import {
  PhotoCardEditorContextType,
  usePhotoCardEditor,
} from 'contexts/PhotoCardController';

export const usePhotoContext = (
  photoType: EProductPhotoType
): PhotoCardEditorContextType | any => {
  try {
    switch (photoType) {
      case EProductPhotoType.PHOTO_CARD:
        return usePhotoCardEditor();
      case EProductPhotoType.MINI_SNAP:
        return usePhotoCardEditor();
      case EProductPhotoType.WIDE_SNAP:
        return usePhotoCardEditor();
      default:
        console.error(`Context for ${photoType} is not available`);
    }
  } catch (error) {
    console.error(`Context for ${photoType} is not available`, error);
  }
};
