import { AxiosResponse } from 'axios';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { routerPages } from '@/constants/routerPath';
import moment from 'moment';
import {
  ICreateCustomProductForGuestResponse,
  ICreateCustomProductRequest,
} from '@/types/imageEditor';
import { apiClient, apiPublic } from '@/utils/api';
import { cartKeys, ordersKeys } from '@/constants/queryKey';

export function useCreateCustomProductForMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: ICreateCustomProductRequest) => {
      return apiClient.post(`/api/custom-product`, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ordersKeys.all,
      });
      queryClient.invalidateQueries({
        queryKey: cartKeys.getAll,
      });
      setTimeout(() => {
        // router.push(`${routerPages.cart}?from=custom-product`);
      }, 100);
    },
  });
}

export function useCreateCustomProductForGuest() {
  // const router = useRouter();

  return useMutation({
    mutationFn: async (payload: ICreateCustomProductRequest) => {
      return apiPublic.post<ICreateCustomProductForGuestResponse>(
        `/api/custom-product/guest`,
        payload
      );
    },

    onSuccess: (
      response: AxiosResponse<ICreateCustomProductForGuestResponse>
    ) => {
      const exp = moment().add(1, 'hour');
      // router.push(
      //   `${routerPages.customProductOrderCode}?code=${response.data.order_code}&exp=${exp.toISOString()}`
      // );
    },
  });
}
