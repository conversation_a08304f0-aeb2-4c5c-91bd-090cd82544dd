import useUploadImage from 'hooks/useUploadImage';
import Konva from 'konva';
import React from 'react';
import { dataURLToBlob } from 'utils/image';

export default function useExportPhoto() {
  const uploadImageMutation = useUploadImage();

  const handleExport = async (
    stageRef: React.MutableRefObject<Konva.Stage | null>
  ): Promise<string> => {
    try {
      if (stageRef.current) {
        const stage = stageRef?.current;
        if (!stage) {
          return '';
        }
        const dataURL = stage.toDataURL();
        const blob = await dataURLToBlob(dataURL);
        const file = new File([blob], 'konva-export.png', {
          type: 'image/png',
        });

        // Create FormData
        const formData = new FormData();
        formData.append('image', file);

        const response = await uploadImageMutation.mutateAsync(formData);
        return response.data.image_url || '';
      } else {
        return '';
      }
    } catch (error) {
      console.error(error);
      return '';
    }
  };

  return {
    handleExport,
  };
}
