import { View } from '@tarojs/components';
import { PropsWithChildren } from 'react';

type GridProps = PropsWithChildren<{
  templateColumns?: string;
  templateRows?: string;
  gap?: number;
  className?: string;
  autoFlow?: 'row' | 'column' | 'dense';
  autoRows?: string;
  autoColumns?: string;
  templateAreas?: string;
  inline?: boolean;
}>;

export default function Grid({
  children,
  templateColumns = 'repeat(1, 1fr)',
  templateRows,
  gap = 0,
  className = '',
  autoFlow,
  autoRows,
  autoColumns,
  templateAreas,
  inline = false,
}: GridProps) {
  const getGridTemplateColumns = () => {
    if (templateColumns === 'repeat(1, 1fr)') return 'grid-cols-1';
    if (templateColumns === 'repeat(2, 1fr)') return 'grid-cols-2';
    if (templateColumns === 'repeat(3, 1fr)') return 'grid-cols-3';
    if (templateColumns === 'repeat(4, 1fr)') return 'grid-cols-4';
    if (templateColumns === 'repeat(5, 1fr)') return 'grid-cols-5';
    if (templateColumns === 'repeat(6, 1fr)') return 'grid-cols-6';
    return '';
  };

  const getGridTemplateRows = () => {
    if (templateRows === 'repeat(1, 1fr)') return 'grid-rows-1';
    if (templateRows === 'repeat(2, 1fr)') return 'grid-rows-2';
    if (templateRows === 'repeat(3, 1fr)') return 'grid-rows-3';
    if (templateRows === 'repeat(4, 1fr)') return 'grid-rows-4';
    if (templateRows === 'repeat(5, 1fr)') return 'grid-rows-5';
    if (templateRows === 'repeat(6, 1fr)') return 'grid-rows-6';
    return '';
  };

  const getAutoFlow = () => {
    if (autoFlow === 'row') return 'grid-flow-row';
    if (autoFlow === 'column') return 'grid-flow-col';
    if (autoFlow === 'dense') return 'grid-flow-dense';
    return '';
  };

  const gapClass = gap > 0 ? `gap-${gap}` : '';
  const displayClass = inline ? 'inline-grid' : 'grid';
  const templateColumnsClass = getGridTemplateColumns();
  const templateRowsClass = getGridTemplateRows();
  const autoFlowClass = getAutoFlow();

  return (
    <View
      className={`${displayClass} ${templateColumnsClass} ${templateRowsClass} ${gapClass} ${autoFlowClass} ${className}`}
    >
      {children}
    </View>
  );
}
