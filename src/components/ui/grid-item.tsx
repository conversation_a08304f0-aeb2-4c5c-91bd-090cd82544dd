import { View } from '@tarojs/components';
import { PropsWithChildren } from 'react';

type GridItemProps = PropsWithChildren<{
  colSpan?: number;
  rowSpan?: number;
  colStart?: number;
  colEnd?: number;
  rowStart?: number;
  rowEnd?: number;
  className?: string;
}>;

export default function GridItem({
  children,
  colSpan,
  rowSpan,
  colStart,
  colEnd,
  rowStart,
  rowEnd,
  className = '',
}: GridItemProps) {
  const getColSpan = () => {
    if (!colSpan) return '';
    return `col-span-${colSpan}`;
  };

  const getRowSpan = () => {
    if (!rowSpan) return '';
    return `row-span-${rowSpan}`;
  };

  const getColStart = () => {
    if (!colStart) return '';
    return `col-start-${colStart}`;
  };

  const getColEnd = () => {
    if (!colEnd) return '';
    return `col-end-${colEnd}`;
  };

  const getRowStart = () => {
    if (!rowStart) return '';
    return `row-start-${rowStart}`;
  };

  const getRowEnd = () => {
    if (!rowEnd) return '';
    return `row-end-${rowEnd}`;
  };

  const colSpanClass = getColSpan();
  const rowSpanClass = getRowSpan();
  const colStartClass = getColStart();
  const colEndClass = getColEnd();
  const rowStartClass = getRowStart();
  const rowEndClass = getRowEnd();

  return (
    <View
      className={`${colSpanClass} ${rowSpanClass} ${colStartClass} ${colEndClass} ${rowStartClass} ${rowEndClass} ${className}`}
    >
      {children}
    </View>
  );
}
