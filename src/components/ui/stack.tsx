import { View } from '@tarojs/components';
import { PropsWithChildren } from 'react';

type JustifyContent =
  | 'start'
  | 'end'
  | 'center'
  | 'between'
  | 'around'
  | 'evenly';
type AlignItems = 'start' | 'end' | 'center' | 'baseline' | 'stretch';

type StackProps = PropsWithChildren<{
  direction?: 'vertical' | 'horizontal';
  gap?: number;
  className?: string;
  justify?: JustifyContent;
  align?: AlignItems;
}>;

const justifyMap: Record<JustifyContent, string> = {
  start: 'justify-start',
  end: 'justify-end',
  center: 'justify-center',
  between: 'justify-between',
  around: 'justify-around',
  evenly: 'justify-evenly',
};

const alignMap: Record<AlignItems, string> = {
  start: 'items-start',
  end: 'items-end',
  center: 'items-center',
  baseline: 'items-baseline',
  stretch: 'items-stretch',
};

export default function Stack({
  children,
  direction = 'vertical',
  gap = 0,
  className = '',
  justify,
  align,
}: StackProps) {
  const flexDirection = direction === 'vertical' ? 'flex-col' : 'flex-row';
  const gapClass = gap > 0 ? `gap-${gap}` : '';
  const justifyClass = justify ? justifyMap[justify] : '';
  const alignClass = align ? alignMap[align] : '';

  return (
    <View
      className={`flex ${flexDirection} ${gapClass} ${justifyClass} ${alignClass} ${className}`}
    >
      {children}
    </View>
  );
}
