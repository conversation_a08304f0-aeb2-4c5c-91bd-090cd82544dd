import React from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { ArrowRight } from '@taroify/icons';
import IP from './Ip';

export interface IpItem {
  frame_id: number;
  frame_image_url: string;
  frame_name: string;
}

interface IpGridProps {
  data?: IpItem[];
  height?: string;
  lineClamp?: number;
  view?: 'corner' | 'all';
}

const IpGrid: React.FC<IpGridProps> = ({
  data,
  height = '32px',
  lineClamp = 2,
  view = 'corner',
}) => {
  const onClickSeeMore = () => {
    Taro.navigateTo({
      url: '/pages/all-categories/index',
    });
  };

  const handleItemClick = (frameId: number) => {
    Taro.navigateTo({
      url: `/pages/ip-page/index?id=${frameId}`,
    });
  };

  return (
    <View
      className={`w-full grid ${view === 'corner' ? 'grid-cols-5 gap-y-5 gap-x-3' : 'grid-cols-3 gap-3'}`}
    >
      {(data?.length && data.length > 10 && view === 'corner'
        ? data?.slice(0, 9)
        : data
      )?.map((item) => (
        <View
          key={item.frame_id}
          className="cursor-pointer"
          onClick={() => handleItemClick(item.frame_id)}
        >
          <IP category={item} />
        </View>
      ))}

      {data && data.length > 10 && view === 'corner' && (
        <View onClick={onClickSeeMore}>
          <View className="relative aspect-square w-full rounded-full overflow-hidden bg-gray-25 flex items-center justify-center">
            <ArrowRight className="text-gray-600" size={20} />
          </View>
          <Text className="text-primary text-center mt-1.5 font-medium line-clamp-1 text-xs h-8">
            더 보기
          </Text>
        </View>
      )}
    </View>
  );
};

export default IpGrid;
