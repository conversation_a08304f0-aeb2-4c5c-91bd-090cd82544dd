import React from 'react';
import { View, Text } from '@tarojs/components';
import ImageArea from '../ImageArea';

interface Category {
  frame_image_url?: string;
  frame_name: string;
}

interface IPProps {
  category: Category;
}

const IP: React.FC<IPProps> = ({ category }) => {
  return (
    <View className="flex flex-col items-center">
      <View className="relative aspect-square w-full rounded-full overflow-hidden">
        <ImageArea
          className="aspect-square w-full h-full object-contain"
          imageUrl={
            category?.frame_image_url ||
            `https://picsum.photos/200/300?id=${Math.random()}`
          }
        />
      </View>

      <Text className="text-primary text-center mt-1.5 font-medium line-clamp-2 text-xs h-8">
        {category.frame_name}
      </Text>
    </View>
  );
};

export default IP;
