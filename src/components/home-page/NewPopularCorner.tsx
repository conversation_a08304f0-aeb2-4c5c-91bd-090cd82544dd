import { View } from '@tarojs/components';
import ProductList, { EProductListLayout } from '../common/product-list';
import { ECornerLayout, IResponseHomeTabCorner } from '../../types/corners';
import { Corner } from './corners/Corner';
import SeeAllProducts from './corners/SeeAllProducts';

export const NewPopularCorner = ({
  data,
}: {
  data: IResponseHomeTabCorner;
}) => {
  const listProducts =
    data.new_popular_response?.simple_product_responses || [];
  const layout = data.new_popular_response?.corner_layout;

  if (layout === ECornerLayout.GRID) {
    return (
      <Corner title={data.title} description={data.subtitle}>
        <View className="flex flex-col gap-6">
          <ProductList
            layout={EProductListLayout.GRID}
            products={listProducts?.slice(0, 6)}
          />
          {listProducts.length > 6 && (
            <SeeAllProducts
              products={listProducts}
              title={data.title}
              subtitle={data.subtitle}
            />
          )}
        </View>
      </Corner>
    );
  }

  if (layout === ECornerLayout.CAROUSEL) {
    return (
      <Corner title={data.title} description={data.subtitle}>
        <View className="flex flex-col gap-6 w-full">
          <ProductList
            layout={EProductListLayout.CAROUSEL}
            products={listProducts?.slice(0, 10)}
          />
          {listProducts.length > 10 && (
            <SeeAllProducts
              products={listProducts}
              title={data.title}
              subtitle={data.subtitle}
            />
          )}
        </View>
      </Corner>
    );
  }

  if (layout === ECornerLayout.CURATION_CARD) {
    return (
      <ProductList
        layout={EProductListLayout.CURATION_CARD}
        products={listProducts}
        curationCardProps={{
          title: data.title,
          subtitle: data.subtitle,
          thumbnail:
            data.new_popular_response?.curation_card_thumbnail_image_url,
        }}
      />
    );
  }

  return <View></View>;
};
