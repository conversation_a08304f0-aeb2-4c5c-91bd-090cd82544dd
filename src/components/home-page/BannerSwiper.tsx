import { Swiper, Image } from '@taroify/core';

import { View } from '@tarojs/components';
import { useState } from 'react';
import Stack from '../ui/stack';
import BannerItem, { PromotionBannerItem } from './BannerItem';

export const MOCK_BANNER_ITEMS: PromotionBannerItem[] = [
  {
    ba_seq: 1,
    app_banner_data: {
      image_url: 'https://picsum.photos/400/300',
      main_text: 'Banner 1',
      description: 'Description 1',
    },
  },
  {
    ba_seq: 2,
    app_banner_data: {
      image_url: 'https://picsum.photos/200/300',
      main_text: 'Banner 2',
      description: 'Description 2',
    },
  },
  {
    ba_seq: 3,
    app_banner_data: {
      image_url: 'https://picsum.photos/300/300',
      main_text: 'Banner 3',
      description: 'Description 3',
    },
  },
];

const BannerSwiper = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  return (
    <Stack
      direction="vertical"
      className="relative overflow-hidden min-h-[428.75px] w-[calc(100% + 32px)]  pt-4"
    >
      <Swiper
        autoplay={3000}
        onChange={setActiveIndex}
        className="h-full w-full"
      >
        {MOCK_BANNER_ITEMS.map((item, index) => (
          <Swiper.Item key={item.ba_seq}>
            <BannerItem
              item={item}
              isActive={activeIndex === index}
              isPrev={activeIndex === index + 1}
            />
          </Swiper.Item>
        ))}
        <Swiper.Indicator className="absolute bottom-8 right-4 bg-black/60 px-2 py-1 rounded text-center text-sm text-white">
          {activeIndex + 1}/{MOCK_BANNER_ITEMS.length}
        </Swiper.Indicator>
      </Swiper>
    </Stack>
  );
};

export default BannerSwiper;
