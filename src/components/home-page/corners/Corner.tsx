import { PropsWithChildren } from 'react';
import { View, Text } from '@tarojs/components';
import Stack from '../../ui/stack';

type CornerProps = {
  title: string;
  description: string;
  className?: string;
};

export const Corner = ({
  title,
  description,
  children,
  className = '',
}: PropsWithChildren<CornerProps>) => {
  return (
    <Stack direction="vertical" className={`items-start  ${className}`} gap={1}>
      <Text className="text-xl font-semibold text-black line-clamp-2">
        {title}
      </Text>

      <Text className="text-sm text-gray-500 mb-4 line-clamp-1">
        {description}
      </Text>

      {children}
    </Stack>
  );
};

export default Corner;
