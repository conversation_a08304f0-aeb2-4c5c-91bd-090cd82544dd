import { View, Text } from '@tarojs/components';
import { AtDrawer } from 'taro-ui';
import Stack from '../../ui/stack';
import { useState } from 'react';
import {
  SimpleProductItem,
  BasicProductCard,
} from '@/components/common/product';
import { Button, Grid, Image } from '@taroify/core';
import HeaderSlots from '@/components/common/headerItem/HeaderSlots';
import SearchProductIcon from '@/components/common/headerItem/SearchProductIcon';
import CartIcon from '@/components/common/headerItem/CartIcon';

type SeeAllProductsProps = {
  products?: SimpleProductItem[];
  title: string;
  subtitle: string;
  triggerElement?: React.ReactElement;
  replaceTitleElement?: React.ReactElement;
};

const SeeAllProducts = ({
  products,
  title,
  subtitle,
  triggerElement,
  replaceTitleElement,
}: SeeAllProductsProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <View>
      {triggerElement || (
        <Button className="w-full" size="mini" onClick={() => setIsOpen(true)}>
          더 보기
        </Button>
      )}

      <AtDrawer
        show={isOpen}
        right
        onClose={() => setIsOpen(false)}
        width="100%"
      >
        <Stack direction="vertical" gap={0} className="h-full bg-white">
          <View className="flex flex-row justify-between items-center h-14 px-4">
            <View onClick={() => setIsOpen(false)} className="w-6 h-6">
              {/* <Image src={iconChevronLeft} className="w-full h-full" /> */}
              Back
            </View>
            <HeaderSlots>
              <SearchProductIcon />
              <CartIcon />
            </HeaderSlots>
          </View>

          <Stack direction="vertical" className="flex-grow" gap={0}>
            {replaceTitleElement || (
              <Stack direction="vertical" className="py-6" gap={1}>
                <Text className="text-xl font-semibold text-center line-clamp-1">
                  {title}
                </Text>
                <Text className="text-sm text-gray-400 text-center line-clamp-1">
                  {subtitle}
                </Text>
              </Stack>
            )}

            <Grid columns={2} className="py-6" bordered={false} gutter={3}>
              {products?.map((product) => (
                <Grid.Item key={product.product_id}>
                  <BasicProductCard product={product} isDepthPage />
                </Grid.Item>
              ))}
            </Grid>
          </Stack>
        </Stack>
      </AtDrawer>
    </View>
  );
};

export default SeeAllProducts;
