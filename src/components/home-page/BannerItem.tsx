import Stack from '@/components/ui/stack';
import { Image, View, Text } from '@tarojs/components';

export type PromotionBannerItem = {
  ba_seq: number;
  app_banner_data: {
    main_text: string;

    description: string;
    image_url: string;
  };
};

const BannerItem = ({
  item,
  isActive,
  isPrev,
}: {
  item: PromotionBannerItem;
  isActive: boolean;
  isPrev: boolean;
}) => {
  return (
    <View className="aspect-[4/5] overflow-hidden rounded-xl">
      <Stack
        className="relative h-full rounded-xl overflow-hidden transition-[height] duration-250 ease-in-out"
        // onClick={handleClickBanner}
      >
        <Image
          className="protected-element h-full w-full object-cover relative z-[1]"
          src={item.app_banner_data.image_url || '/images/ImageArea.png'}
        />
        <View className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-[2]" />
        <Stack
          direction="vertical"
          className="absolute bottom-0 w-full p-8 gap-3 z-[3]"
        >
          <Text className="text-xl font-semibold text-white line-clamp-2">
            {item.app_banner_data.main_text}
          </Text>
          <Text className="text-sm text-white line-clamp-3">
            {item.app_banner_data.description}
          </Text>
        </Stack>
      </Stack>
    </View>
  );
};

export default BannerItem;
