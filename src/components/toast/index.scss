.custom-toast {
  position: fixed;
  bottom: 20px;
  left: 24px;
  right: 24px;
  z-index: 9999;
  padding: 16px;
  border-radius: 12px;
  animation: slideUp 0.2s cubic-bezier(0.4, 0, 0.4, 1) forwards;

  &--success {
    background-color: #00A63A;
  }

  &--error {
    background-color: #D93B3B;
  }

  &--loading {
    background-color: #FFFFFF;
  }

  &--info {
    background-color: #09090B;
  }

  &.toast-exit {
    animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.4, 1) forwards;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(20px);
    opacity: 0;
  }
} 