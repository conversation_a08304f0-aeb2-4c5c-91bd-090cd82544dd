import React, { useState } from 'react';
import { View, Text } from '@tarojs/components';
import { ToastType, useToastStore } from '@/stores/toastStore';
import { Passed, WarningOutlined } from '@taroify/icons';
import { Loading } from '@taroify/core';
import Stack from '../ui/stack';
import './index.scss';

const ICONS: Record<ToastType, React.ReactNode> = {
  info: null,
  loading: <Loading className="w-4 h-4" style={{ color: '#006BD6' }} />,
  success: <Passed className="w-4 h-4" style={{ color: '#fff' }} />,
  error: <WarningOutlined className="w-4 h-4" style={{ color: '#fff' }} />,
};

const Toast: React.FC = () => {
  const { visible, title, description, type, duration, actions, hideToast } =
    useToastStore();
  const [isExiting, setIsExiting] = useState(false);

  React.useEffect(() => {
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        setIsExiting(true);
        setTimeout(() => {
          hideToast();
          setIsExiting(false);
        }, 200);
      }, duration - 200);
      return () => clearTimeout(timer);
    }
  }, [visible, duration, hideToast]);

  if (!visible) return null;

  return (
    <View
      className={`custom-toast custom-toast--${type} ${isExiting ? 'toast-exit' : ''}`}
    >
      <Stack className="flex flex-row justify-between gap-4">
        <Stack className="flex flex-row items-center gap-3 w-full">
          {ICONS[type]}
          <Stack className="flex flex-col gap-1">
            <Text
              className={`text-base font-medium ${type === 'loading' ? 'text-[#09090B]' : 'text-white'}`}
            >
              {title}
            </Text>
            {description && (
              <Text
                className={`text-sm  ${type === 'loading' ? 'text-[#09090B]' : 'text-white'}`}
              >
                {description}
              </Text>
            )}
          </Stack>
        </Stack>

        {actions && actions}
      </Stack>
    </View>
  );
};

export default Toast;
