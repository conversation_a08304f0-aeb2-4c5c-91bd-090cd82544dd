import { Image } from '@taroify/core';
import Taro from '@tarojs/taro';
import { Text, View } from '@tarojs/components';
import heartFilledIcon from '../../../assets/icons/icon-heart-filled.svg';
import heartIcon from '../../../assets/icons/icon-heart.svg';
import { ECornerLayout } from '../../../types/corners';
import { EProductType } from '../../../types/product';

import ImageArea from '../../../components/ImageArea';
import Stack from '../../ui/stack';

export interface ProductImage {
  product_image_type: string;
  image_url: string;
}

export interface ProductCountryPrice {
  national_name: string;
  currency_code: string;
  product_price: number;
}

export type SimpleProductItem = {
  frame_id: number;
  frame_name: string;
  product_id: number;
  product_name: string;
  product_image_url?: string;
  product_images: ProductImage[];
  product_type: string;
  product_category: EProductType;
  is_like: boolean;
  product_country_prices: ProductCountryPrice[];
};

type ProductProps = {
  direction?: 'row' | 'column';
  layout?: ECornerLayout;
  product: SimpleProductItem;
  isDepthPage?: boolean;
};

export const BasicProductCard = ({
  direction = 'column',
  layout = ECornerLayout.GRID,
  product,
  isDepthPage = false,
}: ProductProps) => {
  const imageUrl =
    product?.product_image_url ?? product?.product_images?.[0]?.image_url;

  const renderLikeIcon = () => {
    return (
      <View className="w-[24px] h-[19px]">
        {product.is_like ? (
          <Image src={heartFilledIcon} className="w-full h-full" />
        ) : (
          <Image src={heartIcon} className="w-full h-full" />
        )}
      </View>
    );
  };

  const renderProductImage = (params: { iconSlot?: JSX.Element } = {}) => {
    return (
      <View className="relative aspect-[2/3] rounded-lg overflow-hidden border border-gray-200 border-solid">
        <ImageArea src={imageUrl} />
        {params.iconSlot}
      </View>
    );
  };

  const renderProductInfo = () => {
    return (
      <Stack direction="vertical" gap={1} className="pr-4">
        <Text
          className={`font-semibold line-clamp-1 ${
            isDepthPage
              ? 'text-sm'
              : layout === ECornerLayout.CAROUSEL
                ? 'text-base'
                : 'text-sm'
          }`}
        >
          {product.frame_name}
        </Text>
        <Text
          className={`line-clamp-1 ${
            isDepthPage
              ? 'text-base'
              : layout === ECornerLayout.GRID
                ? 'text-base'
                : 'text-base'
          }`}
        >
          {product.product_name}
        </Text>
        {isDepthPage && (
          <Text className="text-sm text-gray-400">
            {product.product_category}
          </Text>
        )}
        <Text
          className={`font-semibold text-black ${
            isDepthPage
              ? 'text-base'
              : layout === ECornerLayout.CAROUSEL
                ? 'text-lg'
                : layout === ECornerLayout.GRID
                  ? 'text-base'
                  : 'text-base'
          }`}
        >
          {product.product_country_prices[0].product_price}원
        </Text>
      </Stack>
    );
  };

  const goToProductDetail = () => {
    Taro.navigateTo({
      url: `/pages/product/index?id=${product.product_id}`,
    });
  };

  if (direction === 'column') {
    return (
      <View className="overflow-hidden [&>*:first-child]:mb-3">
        {renderProductImage({
          iconSlot: (
            <View className="absolute bottom-2.5 right-2.5">
              {renderLikeIcon()}
            </View>
          ),
        })}
        {renderProductInfo()}
      </View>
    );
  }

  return (
    <View onClick={goToProductDetail}>
      <Stack
        direction="horizontal"
        gap={4}
        className="overflow-hidden h-full"
        align="center"
        justify="around"
      >
        <View className="flex-1 flex-shrink-0 w-[4.1875rem]">
          {renderProductImage()}
        </View>
        <View className="grow-0 shrink-0 basis-[12.5rem] flex-3">
          {renderProductInfo()}
        </View>
        <View className="flex-1 justify-end">{renderLikeIcon()}</View>
      </Stack>
    </View>
  );
};

export default BasicProductCard;
