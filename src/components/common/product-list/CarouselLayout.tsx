import { View } from '@tarojs/components';
import { Image, Swiper } from '@taroify/core';
import { BasicProductCard, SimpleProductItem } from '../product';

import { ECornerLayout } from '../../../types/corners';
import ImageArea from '../../../components/ImageArea';

type CarouselLayoutProps = {
  products: SimpleProductItem[];
  isDepthPage?: boolean;
  className?: string;
};

export const CarouselLayout = ({
  products,
  isDepthPage,
  className,
}: CarouselLayoutProps) => {
  console.log(products, 'products');
  return (
    <View
      className={`overflow-hidden w-[calc(100% + 32px)] ${className || ''}`}
    >
      <Swiper className="h-full w-full">
        {products.map((product) => (
          <Swiper.Item key={product.product_id}>
            <BasicProductCard
              isDepthPage={isDepthPage}
              product={product}
              layout={ECornerLayout.CAROUSEL}
            />
          </Swiper.Item>
        ))}
      </Swiper>
    </View>
  );
};

export default CarouselLayout;
