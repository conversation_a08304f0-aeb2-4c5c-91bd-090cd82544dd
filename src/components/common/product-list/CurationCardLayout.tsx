import { View } from '@tarojs/components';
import { ECornerLayout } from '../../../types/corners';
import ImageArea from '../../ImageArea';
import { BasicProductCard, SimpleProductItem } from '../product';
import SeeAllProducts from '../../home-page/corners/SeeAllProducts';
import IconChevronRight from '../../../assets/icons/icon-chevron-right.svg';
import GradientOverlay from '../GradientOverlay';

type CurationCardLayoutProps = {
  products: SimpleProductItem[];
  title: string;
  subtitle: string;
  thumbnail?: string;
};

const SectionImage = ({
  isDepthPage,
  showIcon,
  title,
  subtitle,
  thumbnail,
  ...rest
}: {
  isDepthPage?: boolean;
  showIcon?: boolean;
  title: string;
  subtitle: string;
  thumbnail?: string;
  [key: string]: any;
}) => {
  return (
    <View
      className={`relative aspect-square overflow-hidden ${
        isDepthPage ? 'rounded-none' : 'rounded-xl'
      }`}
      {...rest}
    >
      <ImageArea src={thumbnail} />
      <GradientOverlay zIndex={2} />
      <View className="absolute bottom-0 left-0 w-full py-6 px-4 z-[3] space-y-1">
        <View className="flex flex-row items-center w-full">
          <View
            className={`flex flex-row items-center text-white whitespace-nowrap line-clamp-2 ${
              isDepthPage ? 'text-[20px]' : 'text-[24px]'
            } font-semibold`}
          >
            {title}
            {showIcon && <IconChevronRight className="w-5 h-5 text-white" />}
          </View>
        </View>
        <View className="text-[14px] text-white line-clamp-1">{subtitle}</View>
      </View>
    </View>
  );
};

export const CurationCardLayout = ({
  products,
  title,
  subtitle,
  thumbnail,
}: CurationCardLayoutProps) => {
  return (
    <View className="space-y-3">
      <SeeAllProducts
        triggerElement={
          <SectionImage
            showIcon
            title={title}
            subtitle={subtitle}
            thumbnail={thumbnail}
          />
        }
        replaceTitleElement={
          <SectionImage
            isDepthPage
            title={title}
            subtitle={subtitle}
            thumbnail={thumbnail}
          />
        }
        products={products}
        title={title}
        subtitle={subtitle}
      />

      <View className="w-full space-y-2">
        {products?.slice(0, 2).map((product) => (
          <View key={product.product_id} className="h-fit">
            <BasicProductCard
              direction="row"
              product={product}
              layout={ECornerLayout.CURATION_CARD}
            />
          </View>
        ))}
      </View>
    </View>
  );
};

export default CurationCardLayout;
