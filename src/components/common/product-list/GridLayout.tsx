import { View } from '@tarojs/components';
import { Grid } from '@taroify/core';
import { BasicProductCard, SimpleProductItem } from '../product';

type GridLayoutProps = {
  products: SimpleProductItem[];
  isDepthPage?: boolean;
  className?: string;
};

export const GridLayout = ({
  products,
  isDepthPage,
  className,
}: GridLayoutProps) => {
  return (
    <Grid
      columns={3}
      bordered={false}
      gutter={3}
      className={`w-full ${className || ''}`}
    >
      {products.map((product) => (
        <Grid.Item key={product.product_id}>
          <BasicProductCard product={product} isDepthPage={isDepthPage} />
        </Grid.Item>
      ))}
    </Grid>
  );
};

export default GridLayout;
