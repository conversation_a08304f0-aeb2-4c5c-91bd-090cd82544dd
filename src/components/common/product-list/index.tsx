import { View } from '@tarojs/components';
import { CarouselLayout } from './CarouselLayout';
import { CurationCardLayout } from './CurationCardLayout';
import { GridLayout } from './GridLayout';
import { SimpleProductItem } from '../product';

export enum EProductListLayout {
  GRID = 'GRID',
  CAROUSEL = 'CAROUSEL',
  CURATION_CARD = 'CURATION_CARD',
}

type ProductListProps = {
  products: SimpleProductItem[];
  layout: EProductListLayout;
  isDepthPage?: boolean;
  className?: string;
  curationCardProps?: {
    title: string;
    subtitle: string;
    thumbnail?: string;
  };
};

const ProductList = ({
  layout,
  products,
  curationCardProps,
  isDepthPage = false,
  className,
}: ProductListProps) => {
  switch (layout) {
    case EProductListLayout.CAROUSEL:
      return (
        <CarouselLayout
          products={products}
          isDepthPage={isDepthPage}
          className={className}
        />
      );

    case EProductListLayout.CURATION_CARD:
      if (!curationCardProps) {
        throw new Error('curationCardProps is required in CurationCardLayout');
      }
      return <CurationCardLayout products={products} {...curationCardProps} />;

    case EProductListLayout.GRID:
    default:
      return (
        <GridLayout
          products={products}
          isDepthPage={isDepthPage}
          className={className}
        />
      );
  }
};

export default ProductList;
