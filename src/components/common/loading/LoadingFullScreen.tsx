import { useEffect, useState } from 'react';
import { View } from '@tarojs/components';
import LoadingAnimation from '@/components/LoadingAnimation';

export default function LoadingFullScreen({ show = true }: { show?: boolean }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted || !show) return null;

  return (
    <View className="fixed z-[9999] max-w-[750px] top-[88px] left-1/2 -translate-x-1/2 w-full h-full flex items-center justify-center bg-white">
      <LoadingAnimation show={show} />
    </View>
  );
}
