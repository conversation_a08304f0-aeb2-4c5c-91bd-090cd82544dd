import { View } from '@tarojs/components';

interface GradientOverlayProps {
  direction?: string;
  className?: string;
  zIndex?: number;
}

const GradientOverlay = ({
  direction = 'to top',
  className = '',
  zIndex = 2,
}: GradientOverlayProps) => {
  const gradientStyle = {
    backgroundImage: `linear-gradient(${direction}, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.00) 60%)`,
  };
  const zIndexClass = zIndex ? `z-[${zIndex}]` : '';

  return (
    <View
      className={`absolute top-0 left-0 h-full w-full ${zIndexClass} ${className}`}
      style={gradientStyle}
    />
  );
};

export default GradientOverlay;
