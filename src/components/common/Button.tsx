import { Button, ButtonProps } from '@tarojs/components';

interface IBaseButtonProps extends ButtonProps {
  variant?: 'outlined' | 'filled';
}

const BaseButton = (props: IBaseButtonProps) => {
  const { variant, className, children, ...rest } = props;
  const baseButtonClass =
    'flex items-center justify-center text-center text-base font-semibold ';
  const variantClass =
    variant === 'outlined'
      ? 'border border-gray-900 text-primary '
      : 'bg-gray-900 text-white ';
  const finalClassName = baseButtonClass
    .concat(variantClass)
    .concat(className || '');

  return (
    <Button className={finalClassName} {...rest}>
      {children}
    </Button>
  );
};

export default BaseButton;
