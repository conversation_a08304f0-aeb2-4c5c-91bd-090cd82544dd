import { Text, View } from '@tarojs/components';
import Stack from '../../ui/stack';

export default function AboutUs() {
  return (
    <Stack
      direction="vertical"
      className="w-full max-w-[600px] gap-5 px-4 pt-10 pb-16 bg-gray-100"
    >
      <Text className="text-base font-semibold text-gray-500">
        주식회사 서북 사업자 정보
      </Text>
      <Stack direction="vertical" className="gap-1">
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">대표자</Text>
          <Text className="text-sm text-gray-500">김민석</Text>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">주소</Text>
          <Text className="text-sm text-gray-500">
            충남 천안시 동남구 만남로 72, 3층 301호
          </Text>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">문의전화</Text>
          <Text className="text-sm text-gray-500">1833-5723</Text>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">이메일</Text>
          <Text className="text-sm text-gray-500"><EMAIL></Text>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">사업자 등록번호</Text>
          <Text className="text-sm text-gray-500">671-88-01814</Text>
          <View
            className="text-sm text-gray-500 font-semibold"
            onClick={() => {
              window.location.href =
                'https://www.ftc.go.kr/bizCommPop.do?wrkr_no=6718801814';
            }}
          >
            사업자 정보 조회
          </View>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">통신판매업 신고번호</Text>
          <Text className="text-sm text-gray-500">제 2021-충남천안-1908호</Text>
        </View>
        <View className="flex flex-row gap-2">
          <Text className="text-sm text-gray-500">호스팅사업자</Text>
          <Text className="text-sm text-gray-500">주식회사 서북</Text>
        </View>
      </Stack>
      <Text className="text-sm text-gray-500">
        Copyright © seobuk Corp. All rights reserved.
      </Text>
    </Stack>
  );
}
