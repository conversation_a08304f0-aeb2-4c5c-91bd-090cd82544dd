import { View } from '@tarojs/components';
import { Image } from '@taroify/core';
import { navigateBack } from '@tarojs/taro';
import iconBack from '@/assets/icons/icon-chevron-left.svg';

interface IBackIconProps {
  customOnGoBack?: () => void;
  position?: 'absolute' | 'unset';
  color?: string;
  pr?: string | number;
}

export default function BackIcon({
  customOnGoBack,
  position = 'unset',
  color,
  pr = 0,
}: IBackIconProps) {
  const handleGoBack = () => {
    if (customOnGoBack) {
      customOnGoBack();
    } else {
      navigateBack();
    }
  };

  return (
    <View
      className={`w-6 h-6 cursor-pointer ${position === 'absolute' ? 'absolute left-3' : ''}`}
      style={{
        paddingRight: pr,
        color: color,
      }}
      onClick={handleGoBack}
    >
      <Image src={iconBack} className="w-6 h-6" />
    </View>
  );
}
