import { Badge, Image } from '@taroify/core';
import cartIcon from '@/assets/icons/icon-bag.svg';
import { routerPages } from '@/constants/routerPath';
import { navigateTo } from '@tarojs/taro';

export default function CartIcon() {
  return (
    <Badge>
      <Image
        src={cartIcon}
        width="24px"
        height="24px"
        className=""
        onClick={() => {
          navigateTo({ url: routerPages.cart });
        }}
      />
    </Badge>
  );
}
