import { Text } from '@tarojs/components';
import React from 'react';

interface IHeaderTitleProps {
  textAlign?: 'left' | 'center' | 'right';
}

export default function HeaderTitle({
  children,
  textAlign = 'left',
}: React.PropsWithChildren<IHeaderTitleProps>) {
  return (
    <Text
      className={`text-xl leading-7 font-semibold text-gray-900 flex-1 truncate text-${textAlign}`}
    >
      {children}
    </Text>
  );
}
