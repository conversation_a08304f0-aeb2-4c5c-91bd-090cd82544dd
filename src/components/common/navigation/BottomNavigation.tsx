import { View, Text, Image } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { routerPages } from '@/constants/routerPath';
import { BOTTOM_NAVIGATION_HEIGHT, CONTAINER_WIDTH } from '@/constants/ui';
import IconHome from '@/assets/icons/icon-home.svg';
import IconHomeActive from '@/assets/icons/icon-home-active.svg';
import IconMenu from '@/assets/icons/icon-menu.svg';
import IconMenuActive from '@/assets/icons/icon-menu-active.svg';
import IconUser from '@/assets/icons/icon-user.svg';
import IconUserActive from '@/assets/icons/icon-user-active.svg';
import { FixedView } from '@taroify/core';

const navMenu = [
  {
    name: '홈',
    icon: IconHome,
    iconActive: IconHomeActive,
    path: routerPages.homePage,
  },
  {
    name: '카테고리',
    icon: IconMenu,
    iconActive: IconMenuActive,
    path: routerPages.categories,
  },
  {
    name: '마이페이지',
    icon: IconUser,
    iconActive: IconUserActive,
    path: routerPages.myPage,
  },
];

const BottomNavigation = () => {
  const pathname = Taro.getCurrentPages().slice(-1)[0]?.route || '';

  const handleNavigation = (path: string) => {
    if (path) {
      Taro.navigateTo({ url: path });
    }
  };

  return (
    <FixedView
      position="bottom"
      className="flex w-full bg-white border-t border-gray-100 z-10 mx-auto"
      style={{
        height: BOTTOM_NAVIGATION_HEIGHT,
        maxWidth: CONTAINER_WIDTH,
      }}
    >
      {navMenu.map((item) => {
        const pathNameWithSlash = `/${pathname}`;
        const isActive = item.path === pathNameWithSlash;

        return (
          <View
            key={item.name}
            className="flex-1 flex flex-col items-center justify-center gap-1 cursor-pointer py-1.5"
            onClick={() => handleNavigation(item.path)}
          >
            {isActive ? (
              <Image src={item.iconActive} className="w-6 h-6" />
            ) : (
              <Image src={item.icon} className="w-6 h-6" />
            )}
            <Text
              className={`text-xs font-semibold ${isActive ? 'text-black' : 'text-gray-600'}`}
            >
              {item.name}
            </Text>
          </View>
        );
      })}
    </FixedView>
  );
};

export default BottomNavigation;
