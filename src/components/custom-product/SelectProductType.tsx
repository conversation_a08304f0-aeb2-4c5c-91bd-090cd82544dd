import { View, Text } from '@tarojs/components';
import Stack from '../ui/stack';
import ProductTypeCard from '@/components/custom-product/ProductTypeCard';
// import HeaderLayout from '@/components/header/HeaderLayout';
import ImagePhotoCard from '@/assets/images/ImagePhotoCard.webp';
import ImageMiniSnap from '@/assets/images/ImageMiniSnap.webp';
import ImageWideSnap from '@/assets/images/ImageWideSnap.webp';
// import BackIcon from "../common/headerItem/BackIcon";
// import HeaderInfoIcon from "../common/headerItem/HeaderInfoIcon";
// import CustomProductGuide from "./CustomProductGuide";

const ProductTypes = [
  {
    id: 1,
    type: '포토카드',
    size: '54x86 (mm)',
    price: '3,000원',
    image: ImagePhotoCard,
    href: '/pages/custom/photocard/index',
  },
  {
    id: 2,
    type: '미니 스냅',
    size: '50x150 (mm)',
    price: '1,500원',
    image: ImageMiniSnap,
    href: '/pages/custom/mini-snap/index',
  },
  {
    id: 3,
    type: '와이드 스냅',
    size: '100x150 (mm)',
    price: '1,500원',
    image: ImageWideSnap,
    href: '/pages/custom/wide-snap/index',
  },
];

const SelectProductType = () => {
  return (
    <View className="relative w-full bg-white mx-auto min-h-screen">
      {/*<HeaderLayout>*/}
      {/*<View className="flex flex-row justify-between items-center px-4 gap-4 relative h-[44px]">*/}
      {/*<BackIcon />*/}
      {/*<HeaderInfoIcon onClick={onOpen} />*/}
      {/*</View>*/}
      {/*</HeaderLayout>*/}
      <View className="w-full">
        <Stack direction="vertical" className="px-4 py-6 gap-[48px]">
          <Stack direction="vertical" gap={4}>
            <View>
              <Text className="text-2xl font-semibold text-black block">
                단 하나뿐인
              </Text>
              <Text className="text-2xl font-semibold text-black block">
                나만의 최애 스냅
              </Text>
            </View>
            <Text className="text-[#8A8F99]">어떤 상품으로 만들어 볼까요?</Text>
          </Stack>
          <Stack direction="vertical" className="gap-[10px]">
            {ProductTypes?.map((item) => (
              <ProductTypeCard key={item.id} item={item} />
            ))}
          </Stack>
        </Stack>
      </View>
      {/*<CustomProductGuide isOpen={open} onClose={onClose} />*/}
    </View>
  );
};

export default SelectProductType;
