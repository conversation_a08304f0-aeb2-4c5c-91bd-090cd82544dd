import React, {
  Dispatch,
  ReactNode,
  SetStateAction,
  useEffect,
  useState,
} from 'react';
import { View, Text } from '@tarojs/components';
import { Image } from '@taroify/core';
import { CONTAINER_WIDTH } from '@/constants/ui';
import { Option, OptionIcon } from '@/types/filter';
import {
  EEditType,
  EImageColor,
  EImageTool,
  EProductPhotoType,
  EProductTool,
  imageToolOptions,
  productToolOptions,
} from '@/constants/image-editor/imageEditor';
import { checkLoggedIn } from '@/utils/auth';
import { EPhotoCardLayout } from '@/constants/image-editor/photocard';
import useGetUserNotificationSetting from '@/hooks/user-notification-setting/useGetUserNotificationSetting';
import { isNetworkOnLine } from '@/utils/common';
import undoIcon from '@/assets/icons/icon-undo.svg';
import redoIcon from '@/assets/icons/icon-redo.svg';
import backIcon from '@/assets/icons/icon-chevron-left.svg';
import closeIcon from '@/assets/icons/icon-close-fluid.svg';
import checkIcon from '@/assets/icons/icon-check.svg';

interface CustomPhotoLayoutProps {
  photoType: EProductPhotoType;
  layoutOptions: OptionIcon[];
  selectedLayout: any;
  setLayout: (layout: any) => void;
  colorOptions: Option[];
  colorMode: EImageColor;
  setColor: (color: EImageColor) => void;
  activeSlot: string | null | number;
  setActiveSlot: Dispatch<SetStateAction<any>>;
  imageEditAction: EImageTool | null;
  setImageEditAction: Dispatch<SetStateAction<EImageTool | null>>;
  handleOrderByMember: () => void;
  handleOrderByGuest: () => void;
  children: ReactNode;
  handleUndo: () => void;
  handleRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  saveToHistory: () => void;
  resetSlotsToPrevious: () => void;
  remindNoImageUploaded: {
    isOpen: boolean;
    onClose: () => void;
  };
  setExportWithMissingImages: Dispatch<SetStateAction<boolean>>;
  openRemindNotAllImagesUploaded: boolean;
  setOpenRemindNotAllImagesUploaded: Dispatch<SetStateAction<boolean>>;
  resetPreviewExportImages: () => void;
  previewImages?: any[];
}

const CustomPhotoLayout: React.FC<CustomPhotoLayoutProps> = ({
  photoType,
  colorOptions,
  layoutOptions,
  children,
  colorMode,
  setColor,
  selectedLayout,
  setLayout,
  activeSlot,
  setActiveSlot,
  setImageEditAction,
  handleUndo,
  handleRedo,
  canUndo,
  canRedo,
  saveToHistory,
  resetSlotsToPrevious,
  handleOrderByMember,
  handleOrderByGuest,
  remindNoImageUploaded,
  setExportWithMissingImages,
  resetPreviewExportImages,
  openRemindNotAllImagesUploaded,
  setOpenRemindNotAllImagesUploaded,
}) => {
  const [currentEditType, setCurrentEditType] = useState<EEditType>(
    EEditType.PRODUCT
  );
  const [productTool, setProductTool] = useState<EProductTool>(
    EProductTool.CHANGE_LAYOUT
  );
  const [isRemindLeavingOpen, setIsRemindLeavingOpen] = useState(false);
  const [isRemindCancelEditOpen, setIsRemindCancelEditOpen] = useState(false);
  const [isConfirmCompleteOpen, setIsConfirmCompleteOpen] = useState(false);
  const [isCopyrightNoticeOpen, setIsCopyrightNoticeOpen] = useState(false);

  const { userNoticeCopyrightEnable } = useGetUserNotificationSetting();

  const isProductEdit = currentEditType === EEditType.PRODUCT;
  const isLayoutTool = productTool === EProductTool.CHANGE_LAYOUT;
  const isLoggedIn = checkLoggedIn();

  const handleApplyChanges = () => {
    saveToHistory();
    setActiveSlot(null);
    setCurrentEditType(EEditType.PRODUCT);
  };

  const handleCancelEditImage = () => {
    setIsRemindCancelEditOpen(false);
    resetSlotsToPrevious();
    setActiveSlot(null);
    setCurrentEditType(EEditType.PRODUCT);
  };

  const handleEditDone = () => {
    if (!isNetworkOnLine()) {
      return;
    }
    if (isLoggedIn) {
      if (userNoticeCopyrightEnable) {
        setIsCopyrightNoticeOpen(true);
      } else {
        handleOrderByMember();
      }
    } else {
      setIsConfirmCompleteOpen(true);
    }
  };

  const handleCompleteOrder = () => {
    setIsCopyrightNoticeOpen(true);
  };

  const renderLayoutOptions = () => (
    <View className="flex flex-row justify-center items-center h-25">
      {layoutOptions.map((option) => {
        const isSelected = option.value === selectedLayout;

        return (
          <View
            key={option.value}
            className="w-16 h-12 flex justify-center items-center cursor-pointer"
            onClick={() => {
              if (isSelected) return null;
              setLayout(option.value as EPhotoCardLayout);
            }}
          >
            <Image
              className={`${isSelected ? 'opacity-100' : 'opacity-50'}`}
              src={option.icon}
              width="32px"
              height="32px"
            />
          </View>
        );
      })}
    </View>
  );

  const renderColorOptions = () => (
    <View className="flex flex-row justify-center items-center h-25">
      {colorOptions.map((option) => {
        const isSelected = option.label === colorMode;

        return (
          <View
            key={option.value}
            style={{ backgroundColor: option.value as string }}
            className={`w-8 h-8 rounded-full mx-2
             shadow-[0px_0px_3px_3px_rgba(0,0,0,0.14)_inset]
             border-white border-solid
            ${isSelected ? 'border-4' : 'border-2'}`}
            onClick={() => {
              if (isSelected) return null;
              setColor(option.label as EImageColor);
            }}
          />
        );
      })}
    </View>
  );

  const renderProductToolSelector = () => (
    <View className="flex flex-row justify-around items-center h-14">
      {productToolOptions.map((option) => (
        <Text
          key={option.value}
          className={`text-base/[40px] font-semibold text-white text-center  flex-1 cursor-pointer ${
            productTool === option.value ? 'opacity-100' : 'opacity-50'
          }`}
          onClick={() => setProductTool(option.value)}
        >
          {option.label}
        </Text>
      ))}
    </View>
  );

  const renderImageTools = () => (
    <View className="flex flex-row justify-center items-center gap-2 h-25">
      {imageToolOptions.map((option) => {
        return (
          <View
            key={option.value}
            className="w-20 h-16 flex justify-center gap-3 items-center cursor-pointer flex-col"
            onClick={() => setImageEditAction(option.value as EImageTool)}
          >
            <Image src={option.icon} width="28px" height="28px" />
            <Text className="text-[11px] text-[#DBDDE1]">{option.label}</Text>
          </View>
        );
      })}
    </View>
  );

  const renderImageControlBar = () => (
    <View className="flex flex-row justify-between items-center h-14 mx-4">
      <Image src={closeIcon} width="24px" height="24px" />
      <Image src={checkIcon} width="24px" height="24px" />
    </View>
  );

  const renderHeader = () => (
    <View className="box-border w-full h-[56px] px-4 flex flex-row justify-between items-center">
      {isProductEdit && (
        <>
          <Image
            src={backIcon}
            width="24px"
            height="24px"
            className="invisible"
          />
          <View className="flex flex-row gap-6">
            <View
              className={`${canUndo ? 'opacity-100' : 'opacity-50'}`}
              onClick={canUndo ? handleUndo : undefined}
            >
              <Image src={undoIcon} width="24px" height="24px" />
            </View>
            <View
              className={`${canRedo ? 'opacity-100' : 'opacity-50'}`}
              onClick={canRedo ? handleRedo : undefined}
            >
              <Image src={redoIcon} width="24px" height="24px" />
            </View>
          </View>
          <Text
            className="text-base text-pink-500 font-semibold cursor-pointer"
            onClick={handleEditDone}
          >
            완료
          </Text>
        </>
      )}
    </View>
  );

  const renderToolbar = () =>
    isProductEdit ? (
      <>
        {isLayoutTool ? renderLayoutOptions() : renderColorOptions()}
        {renderProductToolSelector()}
      </>
    ) : (
      <>
        {renderImageTools()}
        {renderImageControlBar()}
      </>
    );

  const renderBottomSheets = () => (
    <>{/* Bottom sheet components would go here */}</>
  );

  useEffect(() => {
    if (activeSlot) {
      setCurrentEditType(EEditType.IMAGE);
    } else {
      setCurrentEditType(EEditType.PRODUCT);
    }
  }, [activeSlot]);

  return (
    <View
      className="bg-[#292b2e] mx-auto h-screen"
      style={{ maxWidth: CONTAINER_WIDTH }}
    >
      {renderHeader()}

      <View className="flex-1">{children}</View>

      {renderToolbar()}
      {renderBottomSheets()}
    </View>
  );
};

export default CustomPhotoLayout;
