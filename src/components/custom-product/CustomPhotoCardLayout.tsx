import { useEffect, useMemo } from 'react';
import { usePhotoCardEditor } from '@/contexts/PhotoCardController';
// import { FORM_COMMON_MESSAGE } from '@/constants/common-messages';
import {
  EPhotoCardFace,
  EPhotoCardLayout,
  PHOTO_CARD_COMMON_COLOR_OPTIONS,
  PHOTO_CARD_DEFAULT_COLOR_OPTIONS,
  PHOTO_CARD_LAYOUT_OPTIONS,
} from '@/constants/image-editor/photocard';
import { EProductPhotoType } from '@/constants/image-editor/imageEditor';
import { useDisclosure } from '@chakra-ui/react-use-disclosure';
import PhotocardImage from '@/components/custom-product/PhotocardImage';
import CustomPhotoLayout from '@/components/custom-product/CustomPhotoLayout';

export default function CustomPhotoCardLayout() {
  const remindNoImageUploaded = useDisclosure();

  const {
    openRemindNotAllImagesUploaded,
    setOpenRemindNotAllImagesUploaded,
    previewExportCard,
    setPreviewExportCard,
    isExportWithMissingImages,
    setExportWithMissingImages,
    setColor,
    colorMode,
    selectedLayout,
    setLayout,
    activeSlot,
    setActiveSlot,
    imageEditAction,
    setImageEditAction,
    handleUndo,
    handleRedo,
    canUndo,
    canRedo,
    saveToHistory,
    resetFaceToPrevious,
    setSaving,
    frontImage,
    frontImageServerUrl,
    setFrontImage,
    selectedFrontLayout,
    backImage,
    backImageServerUrl,
    setBackImage,
    selectedBackLayout,
  } = usePhotoCardEditor();

  const previewImages = [
    {
      url: previewExportCard.FRONT,
      label: '포토카드 앞면',
      width: 202,
      aspectRatio: 200 / 320,
    },
    {
      url: previewExportCard.BACK,
      label: '포토카드 뒷면',
      width: 202,
      aspectRatio: 200 / 320,
    },
  ];

  const colorOptions = useMemo(() => {
    switch (selectedLayout) {
      case EPhotoCardLayout.DEFAULT:
        return PHOTO_CARD_DEFAULT_COLOR_OPTIONS;
      case EPhotoCardLayout.FRAME:
      case EPhotoCardLayout.FULL:
      default:
        return PHOTO_CARD_COMMON_COLOR_OPTIONS;
    }
  }, [selectedLayout]);

  function checkValidMaterials() {
    const isFrontLayoutFull = selectedFrontLayout === EPhotoCardLayout.FULL;
    const isBackLayoutFull = selectedBackLayout === EPhotoCardLayout.FULL;

    if (isFrontLayoutFull && isBackLayoutFull) {
      return true;
    }

    const bothImagesMissing = !frontImage && !backImage;
    if (bothImagesMissing && !isFrontLayoutFull && !isBackLayoutFull) {
      remindNoImageUploaded.onOpen();
      return false;
    }

    if (frontImageServerUrl === '' && frontImage && !isFrontLayoutFull) {
      setFrontImage('');
      // toaster.error({
      //   title: FORM_COMMON_MESSAGE.IMAGE_UPLOAD_ERROR,
      // });
      return false;
    }
    if (backImageServerUrl === '' && backImage && !isBackLayoutFull) {
      setBackImage('');
      // toaster.error({
      //   title: FORM_COMMON_MESSAGE.IMAGE_UPLOAD_ERROR,
      // });
      return false;
    }

    if (!isFrontLayoutFull && !frontImage) {
      setOpenRemindNotAllImagesUploaded(true);
      return false;
    }

    if (!isBackLayoutFull && !backImage) {
      setOpenRemindNotAllImagesUploaded(true);
      return false;
    }

    return true;
  }

  function exportImageProcess() {
    const isValid = checkValidMaterials();
    if (isValid) {
      setSaving(true);
    }
  }

  function resetPreviewExportImages() {
    setPreviewExportCard({
      [EPhotoCardFace.FRONT]: '',
      [EPhotoCardFace.BACK]: '',
    });
  }

  useEffect(() => {
    if (isExportWithMissingImages) {
      setSaving(true);
    }
  }, [isExportWithMissingImages]);

  return (
    <CustomPhotoLayout
      colorOptions={colorOptions}
      colorMode={colorMode}
      setColor={setColor}
      layoutOptions={PHOTO_CARD_LAYOUT_OPTIONS}
      selectedLayout={selectedLayout}
      setLayout={setLayout}
      photoType={EProductPhotoType.PHOTO_CARD}
      activeSlot={activeSlot}
      setActiveSlot={setActiveSlot}
      imageEditAction={imageEditAction}
      setImageEditAction={setImageEditAction}
      handleUndo={handleUndo}
      handleRedo={handleRedo}
      canUndo={canUndo}
      canRedo={canRedo}
      saveToHistory={saveToHistory}
      resetSlotsToPrevious={resetFaceToPrevious}
      handleOrderByGuest={exportImageProcess}
      handleOrderByMember={exportImageProcess}
      remindNoImageUploaded={remindNoImageUploaded}
      openRemindNotAllImagesUploaded={openRemindNotAllImagesUploaded}
      setOpenRemindNotAllImagesUploaded={setOpenRemindNotAllImagesUploaded}
      setExportWithMissingImages={setExportWithMissingImages}
      previewImages={previewImages}
      resetPreviewExportImages={resetPreviewExportImages}
    >
      <PhotocardImage />
    </CustomPhotoLayout>
  );
}
