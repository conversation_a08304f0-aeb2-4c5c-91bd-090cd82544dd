import { View, Image, Text } from '@tarojs/components';
import { navigateTo } from '@tarojs/taro';
import Stack from '@/components/ui/stack';
import React from 'react';

export interface ProductTypeItem {
  id: number;
  type: string;
  size: string;
  price: string;
  image: string;
  href: string;
}

interface ProductTypeCardProps {
  item: ProductTypeItem;
}

const ProductTypeCard: React.FC<ProductTypeCardProps> = ({ item }) => {
  const { type, size, price, image, href } = item;

  const handleClick = () => {
    navigateTo({ url: href });
  };

  return (
    <View
      className="flex flex-row justify-between items-center border border-solid border-[#EFF2F5] rounded-[16px] px-6 pr-0 h-[150px]"
      onClick={handleClick}
    >
      <Stack direction="vertical" gap={2}>
        <View>
          <Text className="text-lg font-semibold text-black block">{type}</Text>
          <Text className="text-sm font-normal text-[#8A8F99] block">
            {size}
          </Text>
        </View>
        <Text className="text-base font-semibold text-black">{price}</Text>
      </Stack>
      <View className="w-[150px] h-[150px]">
        <Image className="w-full h-full" src={image} />
      </View>
    </View>
  );
};

export default ProductTypeCard;
