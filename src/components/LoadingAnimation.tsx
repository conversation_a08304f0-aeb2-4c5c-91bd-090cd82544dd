import { View, Image } from '@tarojs/components';
import { FC } from 'react';

interface LoadingAnimationProps {
  show: boolean;
}

const LoadingAnimation: FC<LoadingAnimationProps> = ({ show }) => {
  return (
    <View
      className={`w-full flex items-center justify-center overflow-hidden transition-all duration-300 ease-in-out ${show ? 'h-20' : 'h-0'}`}
    >
      <Image
        className="h-8 w-8"
        src="/gifs/LoadingAnimation.gif"
        mode="aspectFit"
      />
    </View>
  );
};

export default LoadingAnimation;
