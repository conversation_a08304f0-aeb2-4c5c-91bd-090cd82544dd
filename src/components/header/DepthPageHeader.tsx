import React from 'react';
import HeaderLayout from './HeaderLayout';
import { View } from '@tarojs/components';
import { TOP_NAVIGATION_HEIGHT } from '@/constants/ui';
import HeaderTitle from '@/components/common/headerItem/HeaderTitle';
import HeaderSlots from '@/components/common/headerItem/HeaderSlots';
import BackIcon from '@/components/common/headerItem/BackIcon';

interface IDepthPageHeaderProps {
  page: React.ReactNode;
  customOnGoBack?: () => void;
  hideBackButton?: boolean;
}

export default function DepthPageHeader({
  page,
  customOnGoBack,
  children,
  hideBackButton,
}: React.PropsWithChildren<IDepthPageHeaderProps>) {
  return (
    <HeaderLayout>
      <View
        className="flex justify-center items-center px-4 gap-4 relative"
        style={{ height: TOP_NAVIGATION_HEIGHT }}
      >
        {!hideBackButton && (
          <BackIcon customOnGoBack={customOnGoBack} position="absolute" />
        )}
        <HeaderTitle textAlign="center">{page}</HeaderTitle>
        <View className="absolute right-4">
          <HeaderSlots>{children}</HeaderSlots>
        </View>
      </View>
    </HeaderLayout>
  );
}
