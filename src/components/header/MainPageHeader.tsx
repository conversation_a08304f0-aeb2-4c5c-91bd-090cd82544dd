import React from 'react';
import { View } from '@tarojs/components';
import HeaderLayout from './HeaderLayout';
import { TOP_NAVIGATION_HEIGHT } from '@/constants/ui';
import HeaderSlots from '@/components/common/headerItem/HeaderSlots';
import HeaderTitle from '@/components/common/headerItem/HeaderTitle';

interface IMainPageHeaderProps {
  page: React.ReactNode;
}

export default function MainPageHeader({
  page,
  children,
}: React.PropsWithChildren<IMainPageHeaderProps>) {
  return (
    <HeaderLayout>
      <View
        className="flex flex-row justify-between items-center px-4 gap-4"
        style={{ height: TOP_NAVIGATION_HEIGHT }}
      >
        <HeaderTitle>{page}</HeaderTitle>
        <HeaderSlots>{children}</HeaderSlots>
      </View>
    </HeaderLayout>
  );
}
