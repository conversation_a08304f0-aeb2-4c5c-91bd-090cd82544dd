import React from 'react';
import MainPageHeader from '@/components/header/MainPageHeader';
import CartIcon from '@/components/common/headerItem/CartIcon';
import NotiIcon from '@/components/common/headerItem/NotiIcon';

export interface IBasicMainPageHeaderProps {
  page: React.ReactNode;
  headerActions?: React.ReactNode;
}
export default function BasicMainPageHeader({
  page,
  headerActions,
}: IBasicMainPageHeaderProps) {
  return (
    <MainPageHeader page={page}>
      {headerActions ? (
        headerActions
      ) : (
        <>
          <NotiIcon />
          <CartIcon />
        </>
      )}
    </MainPageHeader>
  );
}
