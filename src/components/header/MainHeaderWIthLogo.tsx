import HeaderLogoIcon from '../common/headerItem/HeaderLogoIcon';
import HeaderSlots from '../common/headerItem/HeaderSlots';
import HeaderLayout from './HeaderLayout';
import Stack from '../ui/stack';

export default function MainHeaderWithLogo({
  children,
}: React.PropsWithChildren) {
  return (
    <HeaderLayout>
      <Stack
        direction="horizontal"
        justify="between"
        align="center"
        className="h-[56px] px-4"
      >
        <HeaderLogoIcon />
        <HeaderSlots>{children}</HeaderSlots>
      </Stack>
    </HeaderLayout>
  );
}
