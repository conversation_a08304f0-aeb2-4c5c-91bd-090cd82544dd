import { Image } from '@taroify/core';
// import { Image } from "@tarojs/components";
import imageArea from '../assets/images/ImageArea.png';

type ImageAreaProps = {
  protectedImage?: boolean;
  imageUrl?: string;
  className?: string;
  [key: string]: any;
};

const ImageArea = ({
  protectedImage = true,
  imageUrl = imageArea,
  className = '',
  ...remainProps
}: ImageAreaProps) => {
  return (
    <Image
      className={`w-full h-full object-cover bg-gray-50 shadow-[0px_0px_24px_0px_rgba(0,0,0,0.04)] ${
        protectedImage ? 'protected-element' : ''
      } ${className}`}
      src={imageUrl}
      mode="aspectFill"
      {...remainProps}
    />
  );
};

export default ImageArea;
