import React from 'react';
import { View } from '@tarojs/components';
import { CONTAINER_WIDTH, TOP_NAVIGATION_HEIGHT } from '@/constants/ui';
import BasicMainPageHeader from '../header/BasicMainPageHeader';
import BottomNavigation from '@/components/common/navigation/BottomNavigation';
import Toast from '../toast';

interface IMainPageLayoutProps {
  title: React.ReactNode;
  children: React.ReactNode;
  headerActions?: React.ReactNode;
}

export default function MainPageLayout({
  title,
  children,
  headerActions,
}: IMainPageLayoutProps) {
  return (
    <View
      className="flex flex-col gap-0 bg-white mx-auto min-h-screen"
      style={{ maxWidth: CONTAINER_WIDTH }}
    >
      <BasicMainPageHeader page={title} headerActions={headerActions} />

      <View
        className="w-full h-full flex flex-col"
        style={{ paddingTop: TOP_NAVIGATION_HEIGHT }}
      >
        {children}
      </View>

      <BottomNavigation />
      <Toast />
    </View>
  );
}
