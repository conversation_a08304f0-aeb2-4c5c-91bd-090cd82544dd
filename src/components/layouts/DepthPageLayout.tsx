import { View } from '@tarojs/components';
import { CONTAINER_WIDTH, TOP_NAVIGATION_HEIGHT } from '@/constants/ui';
import React from 'react';
import DepthPageHeader from '@/components/header/DepthPageHeader';

interface IDepthPageLayoutProps {
  page?: React.ReactNode;
  headerAction?: React.ReactNode;
  customOnGoBack?: () => void;
  hideBackButton?: boolean;
  childrenContainerProp?: {
    className?: string;
    style?: React.CSSProperties;
  };
}

export default function DepthPageLayout({
  children,
  page,
  customOnGoBack,
  headerAction,
  hideBackButton,
  childrenContainerProp = {},
}: React.PropsWithChildren<IDepthPageLayoutProps>) {
  return (
    <View
      className="relative w-full bg-white mx-auto flex flex-col"
      style={{
        maxWidth: CONTAINER_WIDTH,
        minHeight: 'var(--full-app-height)',
      }}
    >
      <DepthPageHeader
        page={page}
        hideBackButton={hideBackButton}
        customOnGoBack={customOnGoBack}
      >
        {headerAction}
      </DepthPageHeader>
      <View
        className={`w-full min-h-dvh ${childrenContainerProp.className || ''}`}
        style={{
          paddingTop: TOP_NAVIGATION_HEIGHT,
          ...childrenContainerProp.style,
        }}
      >
        {children}
      </View>
    </View>
  );
}
