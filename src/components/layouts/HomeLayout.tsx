import { PropsWithChildren } from 'react';
import HomePageHeader from '@/components/header/HomePageHeader';
import Stack from '@/components/ui/stack';
import BottomNavigation from '@/components/common/navigation/BottomNavigation';
import './index.scss';

export default function HomeLayout({ children }: PropsWithChildren) {
  return (
    <Stack
      direction="vertical"
      className="relative bg-white !important mx-auto max-w-[600px]"
      gap={0}
    >
      <HomePageHeader />
      <Stack direction="vertical" gap={0}>
        {children}
      </Stack>
      <BottomNavigation />
    </Stack>
  );
}
