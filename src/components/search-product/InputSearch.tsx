import React, { useState, useRef } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { Search, Close } from '@taroify/icons';
import { Input } from '@taroify/core';

// Types
interface Option {
  frame_id: number;
  frame_name: string;
  frame_category: string;
}

enum ERecentSearchType {
  PRODUCT = 'PRODUCT',
  IP = 'IP',
}

type TRecentSearch = {
  type: ERecentSearchType;
  searchInput: string;
};

interface InputSearchProps {
  placeholder?: string;
  onSearch?: (keyword: string) => void;
  onSuggestionSelect?: (item: Option) => void;
}

const InputSearch: React.FC<InputSearchProps> = ({
  placeholder = 'Search for products',
  onSearch,
  onSuggestionSelect,
}) => {
  // State
  const [keywordSearch, setKeywordSearch] = useState('');
  const [isSearchBarFocused, setIsSearchBarFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<Option[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Refs
  const debounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isReady = useRef<boolean>(false);

  // Utils
  const showClearSearch = keywordSearch.length > 0;

  // Local Storage helpers for Taro
  const getRecentSearches = (): TRecentSearch[] => {
    try {
      const stored = Taro.getStorageSync('search');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  };

  const setRecentSearches = (searches: TRecentSearch[]) => {
    try {
      Taro.setStorageSync('search', JSON.stringify(searches));
    } catch (error) {
      console.error('Failed to save recent searches:', error);
    }
  };

  const isRecentSearchExist = (
    recentSearches: TRecentSearch[],
    target: TRecentSearch
  ) => {
    return recentSearches.some(
      (searchItem) =>
        searchItem.type === target.type &&
        searchItem.searchInput === target.searchInput
    );
  };

  const handleAddToRecentSearch = (value: TRecentSearch) => {
    const recentSearches = getRecentSearches();
    if (isRecentSearchExist(recentSearches, value)) return;

    const newSearches = [value, ...recentSearches].slice(0, 10); // Keep only 10 recent searches
    setRecentSearches(newSearches);
  };

  // Event handlers
  const clearInput = () => {
    setKeywordSearch('');
    setShowSuggestions(false);
    setIsSearchBarFocused(false);
    setSuggestions([]);
  };

  const handleInputChange = (e: any) => {
    const value = e.detail.value;
    setKeywordSearch(value);

    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      if (value.trim()) {
        // Mock API call - replace with actual API
        fetchSuggestions(value.trim());
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);
  };

  const fetchSuggestions = async (keyword: string) => {
    setIsLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockSuggestions: Option[] = [
        {
          frame_id: 1,
          frame_name: `${keyword} Frame 1`,
          frame_category: 'Category A',
        },
        {
          frame_id: 2,
          frame_name: `${keyword} Frame 2`,
          frame_category: 'Category B',
        },
        {
          frame_id: 3,
          frame_name: `${keyword} Frame 3`,
          frame_category: 'Category C',
        },
      ];

      setSuggestions(mockSuggestions);
      setShowSuggestions(true);
      isReady.current = true;
    } catch (error) {
      console.error('Failed to fetch suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputFocus = () => {
    setIsSearchBarFocused(true);
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for suggestion clicks
    setTimeout(() => {
      setIsSearchBarFocused(false);
      setShowSuggestions(false);
    }, 200);
  };

  const handleConfirm = (e: any) => {
    const value = e.detail.value.trim();
    if (value) {
      handleAddToRecentSearch({
        type: ERecentSearchType.PRODUCT,
        searchInput: value,
      });

      onSearch?.(value);
      setShowSuggestions(false);
      setIsSearchBarFocused(false);
    }
  };

  const handleSuggestionClick = (item: Option) => {
    handleAddToRecentSearch({
      type: ERecentSearchType.IP,
      searchInput: item.frame_name,
    });

    onSuggestionSelect?.(item);
    setKeywordSearch(item.frame_name);
    setShowSuggestions(false);
    setIsSearchBarFocused(false);
  };

  const highlightMatchedText = (text: string, keyword: string) => {
    if (!keyword) return text;

    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <Text key={index} className="font-bold text-blue-600">
          {part}
        </Text>
      ) : (
        <Text key={index}>{part}</Text>
      )
    );
  };

  return (
    <View className="relative w-full">
      {/* Input Container */}
      <View className="flex items-center gap-3">
        <View className="relative flex-1">
          <Input
            className="w-full h-full py-2 px-4 bg-gray-50 rounded-xl text-sm"
            placeholder={placeholder}
            value={keywordSearch}
            onInput={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            onConfirm={handleConfirm}
          />

          {/* Search Icon */}
          {!showClearSearch && (
            <View className="absolute right-3 top-1/2 -translate-y-1/2">
              <Search className="text-gray-400" size={16} />
            </View>
          )}

          {/* Clear Button */}
          {showClearSearch && (
            <View
              className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center cursor-pointer"
              onClick={clearInput}
            >
              <Close className="text-white" size={12} />
            </View>
          )}
        </View>

        {/* Cancel Button */}
        {isSearchBarFocused && (
          <Text
            className="text-sm font-semibold text-gray-600 cursor-pointer whitespace-nowrap"
            onClick={clearInput}
          >
            Cancel
          </Text>
        )}
      </View>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <View className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-2 z-50 max-h-80 overflow-y-auto">
          {suggestions.map((item) => (
            <View
              key={item.frame_id}
              className="flex items-center justify-between px-4 py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer"
              onClick={() => handleSuggestionClick(item)}
            >
              <View className="flex-1">
                <Text className="text-sm text-gray-900">
                  {highlightMatchedText(item.frame_name, keywordSearch)}
                </Text>
              </View>
              <Text className="text-xs text-gray-500 ml-2">
                {item.frame_category}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Loading State */}
      {isLoading && (
        <View className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-2 z-50">
          <View className="flex items-center justify-center py-4">
            <Text className="text-sm text-gray-500">Searching...</Text>
          </View>
        </View>
      )}

      {/* No Results */}
      {showSuggestions &&
        !isLoading &&
        suggestions.length === 0 &&
        keywordSearch.trim() && (
          <View className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-2 z-50">
            <View className="flex items-center justify-center py-4">
              <Text className="text-sm text-gray-500">No results found</Text>
            </View>
          </View>
        )}
    </View>
  );
};

export default InputSearch;
