import React, { useState } from 'react';
import { Input, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Router from 'tarojs-router-next';
import { Image } from '@taroify/core';
import IconChevronLeft from '@/assets/icons/icon-chevron-left.svg';

interface HeaderSearchProductProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
}

const HeaderSearchProduct = ({
  searchValue,
  onSearchChange,
}: HeaderSearchProductProps) => {
  const statusBarHeight = Taro.getSystemInfoSync().statusBarHeight || 0;
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue);

  const handleSearchChange = (value: string) => {
    setInternalSearchValue(value);
    onSearchChange?.(value);
  };

  const handleBack = () => {
    Router.back();
  };

  return (
    <View
      className="fixed top-0 left-0 right-0 z-50 border-b border-gray-200 shadow-sm"
      style={{
        paddingTop: `${statusBarHeight}px`,
      }}
    >
      <View className="flex items-center h-[44px] px-2 gap-4">
        <View
          className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-gray-100 transition-colors"
          onClick={handleBack}
        >
          <Image src={IconChevronLeft} className="w-6 h-6" />
        </View>

        <View className="relative flex-1">
          {/* <View className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
            <Search className="text-gray-400" size={16} />
          </View> */}
          <Input
            className="w-full h-9 pl-10 bg-gray-50 rounded-full text-sm"
            placeholder="Search for products"
            value={searchValue || internalSearchValue}
            onInput={(e) => handleSearchChange(e.detail.value)}
            onConfirm={(e) => handleSearchChange(e.detail.value)}
            focus
            style={{
              fontSize: '14px',
              lineHeight: '20px',
            }}
          />

          {(searchValue || internalSearchValue) && (
            <View
              className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center"
              onClick={() => handleSearchChange('')}
            >
              <View className="w-2 h-2 bg-white rounded-full" />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default HeaderSearchProduct;
