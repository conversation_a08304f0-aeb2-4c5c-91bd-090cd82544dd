import React from 'react';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import Router from 'tarojs-router-next';
import { Image } from '@taroify/core';
import IconChevronLeft from '@/assets/icons/icon-chevron-left.svg';
import InputSearch from './InputSearch';

// Types
interface SuggestionItem {
  frame_id: number;
  frame_name: string;
  frame_category: string;
}

interface HeaderSearchProductProps {
  onSearchChange?: (value: string) => void;
  onSuggestionSelect?: (item: SuggestionItem) => void;
}

const HeaderSearchProduct = ({
  onSearchChange,
  onSuggestionSelect,
}: HeaderSearchProductProps) => {
  const statusBarHeight = Taro.getSystemInfoSync().statusBarHeight || 0;

  const handleSearchChange = (value: string) => {
    onSearchChange?.(value);
  };

  const handleBack = () => {
    Router.back();
  };

  return (
    <View
      className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm"
      style={{
        paddingTop: `${statusBarHeight}px`,
      }}
    >
      <View className="flex items-center h-9 px-2 gap-4">
        <View
          className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-gray-100 transition-colors flex-shrink-0"
          onClick={handleBack}
        >
          <Image src={IconChevronLeft} className="w-6 h-6" />
        </View>

        <View className="flex-1 min-w-0">
          <InputSearch
            placeholder="Search for products"
            onSearch={handleSearchChange}
            onSuggestionSelect={onSuggestionSelect}
          />
        </View>
      </View>
    </View>
  );
};

export default HeaderSearchProduct;
