import React, { useEffect, useState } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { Close } from '@taroify/icons';

// Types
enum ERecentSearchType {
  PRODUCT = 'PRODUCT',
  IP = 'IP',
}

type TRecentSearch = {
  type: ERecentSearchType;
  searchInput: string;
};

interface RecentSearchesProps {
  onSearchProduct?: (keyword: string) => void;
  onSearchIP?: (id: string) => void;
}

export const CONDITIONAL_SEARCH = 'CONDITIONAL_SEARCH';

const RecentSearches: React.FC<RecentSearchesProps> = ({
  onSearchProduct,
  onSearchIP,
}) => {
  const [recentSearches, setRecentSearches] = useState<TRecentSearch[]>([]);

  const handleSearchIP = (id: string) => {
    onSearchIP?.(id);
    // Default navigation if no handler provided
    if (!onSearchIP) {
      Taro.navigateTo({
        url: `/pages/ip-page/index?id=${id}`,
      });
    }
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    try {
      Taro.setStorageSync('search', JSON.stringify([]));
    } catch (error) {
      console.error('Failed to clear recent searches:', error);
    }
  };

  const deleteRecentSearch = (searchItem: TRecentSearch) => {
    const updatedSearches = recentSearches.filter((item) => {
      return (
        item.type !== searchItem.type ||
        item.searchInput !== searchItem.searchInput
      );
    });

    try {
      Taro.setStorageSync('search', JSON.stringify(updatedSearches));
      setRecentSearches(updatedSearches);
    } catch (error) {
      console.error('Failed to delete recent search:', error);
    }
  };

  const handleSearchItemClick = (item: TRecentSearch) => {
    if (item.type === ERecentSearchType.IP) {
      handleSearchIP(item.searchInput);
    } else {
      onSearchProduct?.(item.searchInput);
    }
  };

  useEffect(() => {
    try {
      const stored = Taro.getStorageSync('search');
      if (stored) {
        const searches = JSON.parse(stored).slice(0, 20); // Limit to 20 items
        setRecentSearches(searches);
      }
    } catch (error) {
      console.error('Failed to load recent searches:', error);
      setRecentSearches([]);
    }
  }, []);

  if (!recentSearches || recentSearches.length === 0) return null;

  return (
    <View className="mb-8">
      {/* Header */}
      <View className="flex items-center justify-between mb-2">
        <Text className="text-sm font-semibold text-gray-900">최근 검색</Text>
        <Text
          className="text-sm font-semibold text-gray-600 cursor-pointer"
          onClick={clearRecentSearches}
        >
          모두 지우기
        </Text>
      </View>

      {/* Recent Search Tags */}
      <View className="flex flex-wrap gap-2 overflow-x-auto">
        {recentSearches.map((item, index) => (
          <View
            key={`recent-search-${item.type}-${item.searchInput}-${index}`}
            className="flex items-center gap-2 px-3 py-1.5 bg-white border border-gray-300 rounded-full cursor-pointer hover:bg-gray-50 transition-colors flex-shrink-0"
            onClick={() => handleSearchItemClick(item)}
          >
            <Text className="text-sm font-semibold text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis max-w-32">
              {item.searchInput}
            </Text>
            <View
              className="flex items-center justify-center w-4 h-4 cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                deleteRecentSearch(item);
              }}
            >
              <Close className="text-gray-400" size={12} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default RecentSearches;
