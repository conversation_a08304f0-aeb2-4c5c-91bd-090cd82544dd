import Taro from '@tarojs/taro';
import { TokenPayload } from '@/types/auth';
import { jwtDecode } from 'jwt-decode';
import { EStorageKey } from '@/constants/storage';

// export const newSignInStringParams = (socialType: SocialAccountType) =>
//   `just_sign_in=true&social_type=${socialType}`;

export function setAuthorizationStorage(token: string, refreshToken: string) {
  Taro.setStorageSync(EStorageKey.ACCESS_TOKEN, token);
  Taro.setStorageSync(EStorageKey.REFRESH_TOKEN, refreshToken);
}

export function setAuthorization(token: string, refreshToken: string) {
  setAuthorizationStorage(token, refreshToken);
  //   postMessageUpdateAuthentication({
  //     accessToken: token,
  //     refreshToken: refreshToken,
  //   });
}

export function getAccessToken() {
  return Taro.getStorageSync(EStorageKey.ACCESS_TOKEN) || '';
}

export function getAuthorization() {
  const token = getAccessToken();
  if (!token) {
    return undefined;
  }
  return `Bearer ${token}`;
}

export function getRefreshToken() {
  return Taro.getStorageSync(EStorageKey.REFRESH_TOKEN) || '';
}

export function checkLoggedIn() {
  return !!getAuthorization();
}

export function decodeUserToken() {
  const token = getAuthorization();
  if (!token) {
    return null;
  }
  const jwtDecoded: TokenPayload = jwtDecode(token);
  return jwtDecoded;
}

export function getUserId() {
  const token = getAccessToken();
  if (!token) {
    return null;
  }
  const jwtDecoded: TokenPayload = jwtDecode(token);
  return jwtDecoded.sub;
}

export function clearAuth() {
  Taro.removeStorageSync(EStorageKey.ACCESS_TOKEN);
  Taro.removeStorageSync(EStorageKey.REFRESH_TOKEN);
  //   postMessageClearAuth();
}
