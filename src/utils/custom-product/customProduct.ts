import { EPhotoCardLayout } from '@/constants/image-editor/photocard';
import {
  EImageColor,
  FILE_ACCEPT_TYPES,
} from '@/constants/image-editor/imageEditor';
import { IPhotoSlot } from '@/hooks/photo-editor/usePhotoSlot';

export function getPhotoCardLayoutImageUrl(
  layout: EPhotoCardLayout,
  color: EImageColor
) {
  return `/static/images/image-editor/PHOTOCARD/${layout}/${color}.svg`;
}

export function transformSlotData(slot: IPhotoSlot) {
  return {
    img_offset_x: slot.imageOffset.x,
    img_offset_y: slot.imageOffset.y,
    img_rotation: slot.imageRotation,
    img_scale: slot.imageScale,
    raw_img_element_url: slot.imageServerUrl || null,
    img_flip_x: slot.imageFlip.x,
    img_flip_y: slot.imageFlip.y,
  };
}

export const validateFileType = (file: File) => {
  const acceptedTypes = FILE_ACCEPT_TYPES.split(', ');
  if (!acceptedTypes.includes(file.type)) {
    // toaster.error({
    //   title: 'PNG, JPG만 업로드할 수 있어요.',
    // });
    return false;
  }
  return true;
};

export const validateImageResolution = (file: File) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      URL.revokeObjectURL(img.src);
      if (img.width < 800 || img.height < 800) {
        // toaster.error({
        //   title: '사진의 해상도가 낮아 올릴 수 없어요.',
        // });
        resolve(false);
      }
      resolve(true);
    };
    img.src = URL.createObjectURL(file);
  });
};
