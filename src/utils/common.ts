export const formatPrice = (price = 0, currencyCode = 'KRW') => {
  const formattedCurrencyCode = currencyCode === 'N/A' ? 'KRW' : currencyCode;

  const hasDecimals = price % 1 !== 0;
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: formattedCurrencyCode,
    minimumFractionDigits: hasDecimals ? 2 : 0,
    maximumFractionDigits: hasDecimals ? 2 : 0,
  });

  const formattedValue = formatter.format(price);

  if (formattedCurrencyCode === 'CNY') {
    return formattedValue.replace('CN¥', '').trim() + '元 (CNY)';
  }

  if (currencyCode == 'KRW') {
    return formattedValue.replace('₩', '').trim() + '원';
  }

  return formattedValue;
};

export const isNetworkOnLine = () =>
  typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean'
    ? navigator.onLine
    : true;
