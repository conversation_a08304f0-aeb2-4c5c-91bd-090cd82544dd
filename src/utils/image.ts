import { Area } from 'react-easy-crop';
import Resizer from 'react-image-file-resizer';

export const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

export const getCroppedImg = async (
  image: string | null,
  croppedAreaPixels: Area
): Promise<{ blob: Blob; preview: string } | any> => {
  try {
    if (image && croppedAreaPixels) {
      const img = await createImage(image);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = croppedAreaPixels.width;
      canvas.height = croppedAreaPixels.height;

      ctx?.drawImage(
        img,
        croppedAreaPixels.x,
        croppedAreaPixels.y,
        croppedAreaPixels.width,
        croppedAreaPixels.height,
        0,
        0,
        canvas.width,
        canvas.height
      );

      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('Canvas is empty'));
            return;
          }
          const preview = URL.createObjectURL(blob);
          resolve({ blob, preview });
        }, 'image/jpeg');
      });
    }
  } catch (e) {
    console.error('Error creating cropped image:', e);
    throw e;
  }
};

export const getFileSizeInMB = (file: File): number => {
  return file.size / (1024 * 1024);
};

export const resizeImage = (
  file: File,
  maxWidth = 2026,
  maxHeight = 1276
): Promise<File> => {
  return new Promise((resolve) => {
    Resizer.imageFileResizer(
      file,
      maxWidth,
      maxHeight,
      file.type === 'image/png' ? 'PNG' : 'JPEG',
      100,
      0,
      (blob) => {
        const resizedFile = new File([blob as Blob], file.name, {
          type: file.type,
          lastModified: Date.now(),
        });
        const newSizeMB = getFileSizeInMB(resizedFile);
        console.log(
          `Resized image from ${getFileSizeInMB(file).toFixed(2)}MB to ${newSizeMB.toFixed(2)}MB`
        );
        resolve(resizedFile);
      },
      'file'
    );
  });
};

export const dataURLToBlob = async (dataURL: string): Promise<Blob> => {
  const response = await fetch(dataURL);
  return await response.blob();
};

export async function convertBase64ToImageFile(base64: string) {
  const blobData = await dataURLToBlob(base64);
  return new File([blobData], 'product_image', {
    type: base64.includes('image/png') ? 'image/png' : 'image/jpeg',
  });
}
