import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import Taro from '@tarojs/taro';
import { getAccessToken, getAuthorization, getRefreshToken } from './auth';
import { useLoadingStore } from '@/stores/loadingStore';
import { jwtDecode } from 'jwt-decode';
import { EStorageKey } from '@/constants/storage';

const DEFAULT_TIMEOUT = 30 * 1000;

const createInterceptedAxiosInstance = (
  baseConfig = {},
  options: {
    handleAuth?: boolean;
    useLoading?: boolean;
    isLogout?: boolean;
  } = {
    handleAuth: true,
    useLoading: true,
    isLogout: false,
  }
) => {
  const instance = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: DEFAULT_TIMEOUT,
    ...baseConfig,
  });

  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const shouldSkipLoading = config.skipLoading;

      if (options.useLoading && !shouldSkipLoading) {
        useLoadingStore.getState().startLoading();
      }

      if (options.handleAuth || options.isLogout) {
        config.headers.Authorization = getAuthorization();
      }

      config.headers['Accept-Language'] = 'ko';
      return config;
    },
    (error) => {
      if (options.useLoading) {
        useLoadingStore.getState().stopLoading();
      }
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response) => {
      console.log(response, 'response');
      const shouldSkipLoading = response.config.skipLoading;

      if (options.useLoading && !shouldSkipLoading) {
        useLoadingStore.getState().stopLoading();
      }
      return response;
    },
    async (error: AxiosError<unknown>) => {
      const shouldSkipLoading = error.config?.skipLoading;

      if (options.useLoading && !shouldSkipLoading) {
        useLoadingStore.getState().stopLoading();
      }

      if (options.handleAuth && typeof window !== 'undefined') {
        const responseError = error?.response;
        const apiStatus = responseError?.status;
        console.log(apiStatus, 'apiStatus');

        switch (apiStatus) {
          case 401:
            const accessToken = getAccessToken();
            const refreshToken = getRefreshToken();
            const expTime = jwtDecode(accessToken)?.exp;

            if (expTime && expTime * 1000 < Date.now()) {
              try {
                // await fetchTokenByRefreshToken(refreshToken);
              } catch {
                Taro.setStorageSync(EStorageKey.ACCESS_TOKEN, '');
                Taro.setStorageSync(EStorageKey.REFRESH_TOKEN, '');
                // postMessageClearAuth();
              }
            } else {
              Taro.setStorageSync(EStorageKey.ACCESS_TOKEN, '');
              Taro.setStorageSync(EStorageKey.REFRESH_TOKEN, '');
              //   postMessageClearAuth();
            }
            break;
          case 403:
            Taro.setStorageSync(EStorageKey.ACCESS_TOKEN, '');
            Taro.setStorageSync(EStorageKey.REFRESH_TOKEN, '');
            // postMessageClearAuth();
            break;
        }
      }

      return Promise.reject(error?.response || error);
    }
  );

  return instance;
};

const apiPublic = createInterceptedAxiosInstance(
  {},
  {
    handleAuth: false,
    useLoading: true,
  }
);

const apiClient = createInterceptedAxiosInstance(
  {},
  {
    handleAuth: true,
    useLoading: true,
  }
);

const apiClientForLogout = createInterceptedAxiosInstance(
  {},
  {
    isLogout: true,
    handleAuth: false,
    useLoading: true,
  }
);

const handleCommonError = (error?: any) => {
  const errorMessage =
    error?.message || error?.data?.message || 'API 요청 오류';

  //   toaster.error({
  //     title: RESPONSE_KO[errorMessage] || errorMessage,
  //   });
  return errorMessage;
};

export { apiClient, apiPublic, handleCommonError, apiClientForLogout };
