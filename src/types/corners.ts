import { SimpleProductItem } from './product';

export enum ECornerType {
  DATA_DRIVEN = 'DATA_DRIVEN',
  NEW_POPULAR = 'NEW_POPULAR',
  IP_RECOMMENDATION = 'IP_RECOMMENDATION',
}
export const OCornerTypeLabel = {
  DATA_DRIVEN: '데이터 기반',
  NEW_POPULAR: '신규/인기',
  IP_RECOMMENDATION: 'IP 추천',
};

export enum ECornerLayout {
  GRID = 'GRID',
  CAROUSEL = 'CAROUSEL',
  CURATION_CARD = 'CURATION_CARD',
}
export const OCornerLayoutLabel = {
  GRID: '그리드',
  CAROUSEL: '캐러셀',
  CURATION_CARD: '큐레이션 카드',
};

export enum ECornerOrder {
  EARLIEST_RELEASE_DATE = 'EARLIEST_RELEASE_DATE',
  HIGHEST_SALES = 'HIGHEST_SALES',
  MOST_LIKES = 'MOST_LIKES',
  MOST_REVIEWS = 'MOST_REVIEWS',
}
export const OCornerOrderLabel = {
  EARLIEST_RELEASE_DATE: '출시일빠른순',
  HIGHEST_SALES: '판매량높은순',
  MOST_LIKES: '좋아요많은순',
  MOST_REVIEWS: '리뷰많은순',
};

type ICornerBaseInfo = {
  corner_id: number;
  corner_type: ECornerType;
  title: string;
  subtitle: string;
  status: boolean;
};

export interface NewPopularCorner {
  corner_layout: ECornerLayout;
  corner_layout_name: string;
  curation_card_thumbnail_image_url: string;
  simple_product_responses: SimpleProductItem[];
}

export interface DataDrivenCorner {
  corner_data_type: ECornerOrder;
  corner_data_type_name: string;
}

export interface IpRecommendationCorner {
  corner_frame_responses: {
    frame_id: number;
    frame_name: string;
    frame_image_url: string;
  }[];
}

export interface IResponseHomeTabCorner extends ICornerBaseInfo {
  new_popular_response: NewPopularCorner | null;
  data_driven_response: DataDrivenCorner | null;
  ip_recommendation_response: IpRecommendationCorner | null;
  last_modified: string;
  modified_user: string;
}
