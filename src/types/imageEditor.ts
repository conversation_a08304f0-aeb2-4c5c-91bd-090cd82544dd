import {
  EImageColor,
  EProductPhotoType,
} from '@/constants/image-editor/imageEditor';
import { EMiniSnapLayout } from '@/constants/image-editor/miniSnap';
import {
  EPhotoCardFace,
  EPhotoCardLayout,
  EPhotoFace,
} from '@/constants/image-editor/photocard';
import { EWideSnapLayout } from '@/constants/image-editor/wideSnap';

export type TImageOffset = {
  x: number;
  y: number;
};

export type TFlipValue = 1 | -1;

export type TImageFlip = {
  x: TFlipValue;
  y: TFlipValue;
};

export type TPhotoCardExporting = {
  [K in keyof typeof EPhotoCardFace]: string;
};

export interface ICustomProductCacheImageSlot {
  img_offset_x: number;
  img_offset_y: number;
  img_rotation: number;
  img_flip_x: TFlipValue;
  img_flip_y: TFlipValue;
  img_scale: number;
  raw_img_element_url: string | null;
}

export interface ICustomProductFace {
  position: EPhotoFace;
  layout: EPhotoCardLayout | EMiniSnapLayout | EWideSnapLayout;
  color: EImageColor;
  export_img_url: string;
  images: ICustomProductCacheImageSlot[];
}

export interface ICreateCustomProductRequest {
  product_type: EProductPhotoType;
  slots: ICustomProductFace[];
}

export interface ICreateCustomProductForGuestResponse {
  order_code: string;
  custom_product: {
    product_type: EProductPhotoType;
    price: number;
    custom_product_cache_slots: ICustomProductFace[];
  };
}
