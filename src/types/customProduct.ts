import { EProductPhotoType } from '@/constants/image-editor/imageEditor';
import { ICustomProductFace } from './imageEditor';

export interface ICustomProductFaceResponse extends ICustomProductFace {
  id: number;
}

export interface IGetCustomProduct {
  id: number;
  product_type: EProductPhotoType;
  price: number;
  user: {
    user_id: string;
    user_name: string;
  };
  slots: ICustomProductFaceResponse[];
}
