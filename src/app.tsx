import 'whatwg-fetch';
import 'abortcontroller-polyfill/dist/polyfill-patch-fetch';
import 'taro-ui/dist/style/index.scss';
import './app.scss';

import { FC, PropsWithChildren } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from '@taroify/core';
import themeVars from './utils/theme';
import Taro from '@tarojs/taro';
import axios from 'axios';

// import axios from "axios";
// import MpAdapter, {
//   defaultTransformRequest,
// } from "@taro-platform/axios-taro-adapter";

// if (process.env.TARO_ENV !== "h5") {
//   // Let the mini program adapter take effect
//   axios.defaults.adapter = MpAdapter;
//   // Set the default request converter
//   axios.defaults.transformRequest = defaultTransformRequest;
//   // If you have a custom request converter, you can write like this:
//   /*
//   axios.defaults.transformRequest = [defaultTransformRequest, your custom request converter];
//   */
// }

axios.defaults.adapter = function (config) {
  console.log(config, 'config');
  return new Promise((resolve, reject) => {
    Taro.request({
      url: `${config.baseURL}${config.url}`,
      method: config.method,
      data: config.data,
      header: config.headers,
      success: (res) => {
        resolve({
          data: res.data,
          status: res.statusCode,
          headers: res.header,
          statusText: res.errMsg,
          config,
        });
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

const queryClient = new QueryClient();

const App: FC<PropsWithChildren> = ({ children }) => {
  return (
    <ConfigProvider theme={themeVars}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </ConfigProvider>
  );
};

export default App;
