import IconHeartFilled from '@/assets/icons/icon-heart-filled-red.svg';
import IconHeart from '@/assets/icons/icon-heart.svg';
import IconShare from '@/assets/icons/icon-share.svg';
import BaseButton from '@/components/common/Button';
import CartIcon from '@/components/common/headerItem/CartIcon';
import HeaderHomeIcon from '@/components/common/headerItem/HeaderHomeIcon';
import SearchProductIcon from '@/components/common/headerItem/SearchProductIcon';
import { SimpleProductItem } from '@/components/common/product';
import ProductList, {
  EProductListLayout,
} from '@/components/common/product-list';
import { MOCK_BANNER_ITEMS } from '@/components/home-page/BannerSwiper';
import { MOCK_DATA_HOME_TAB_DETAILS } from '@/components/home-page/HomeTab';
import ImageArea from '@/components/ImageArea';
import DepthPageLayout from '@/components/layouts/DepthPageLayout';
import Stack from '@/components/ui/stack';
import { Swiper, Button } from '@taroify/core';
import { Image, Text, View } from '@tarojs/components';
import { useMemo, useState } from 'react';

const ProductPage = () => {
  const sameCollectionProductsDisplay = true; // This should be replaced with actual logic to determine display state
  const isOutOfStock = false; // This should be replaced with actual logic to determine stock status
  const relevantProducts =
    MOCK_DATA_HOME_TAB_DETAILS.corner_responses[0].new_popular_response
      ?.simple_product_responses || []; // This should be replaced with actual logic to fetch relevant products
  const product = {
    is_like: false, // This should be replaced with actual product data
    product_sale_end: false, // This should be replaced with actual product data
  };

  const [activeIndex, setActiveIndex] = useState<number>(0);

  const Icon = useMemo(() => {
    return product?.is_like ? (
      <Image src={IconHeartFilled} className="h-6 w-6" />
    ) : (
      <Image src={IconHeart} className="h-6 w-6" />
    );
  }, [product?.is_like]);

  return (
    <DepthPageLayout
      headerAction={
        <>
          <HeaderHomeIcon />
          <SearchProductIcon />
          <CartIcon />
        </>
      }
    >
      <View>
        <Stack gap={0} className="pb-[144px] bg-white">
          <Stack className="bg-gray-50 p-6 h-[400px] relative">
            <Swiper
              autoplay={3000}
              onChange={setActiveIndex}
              className="basic-swiper h-full w-full"
            >
              {MOCK_BANNER_ITEMS.map((item) => (
                <Swiper.Item key={item.ba_seq}>
                  <Stack gap={0} className="relative h-full w-full">
                    <ImageArea
                      src={item.app_banner_data.image_url}
                      objectFit="contain"
                      opacity={isOutOfStock ? 0.5 : 1}
                    />
                    {isOutOfStock && (
                      <Text className="text-body1 absolute bottom-0 text-red-500">
                        Out of Stock
                      </Text>
                    )}
                  </Stack>
                </Swiper.Item>
              ))}
            </Swiper>
            <Stack className="absolute bg-[#000000BF] right-5 bottom-5 z-3 border-radius-4 px-3 py-1">
              <Text className="text-caption1 text-white">
                {activeIndex + 1} / {MOCK_BANNER_ITEMS.length}
              </Text>
            </Stack>
          </Stack>
          <Stack className="border-gray-50 border-b-[1px solid #EFF2F5] px-4 py-6 gap-[32px]">
            <Stack
              gap={4}
              className={
                sameCollectionProductsDisplay ? 'display-flex' : 'display-none'
              }
            >
              <Text className="text-label1 text-tertiary">
                {'[collection_name]'}
              </Text>
              <Stack className="minH-[84px]">
                <Swiper
                  autoplay={3000}
                  onChange={setActiveIndex}
                  className="basic-swiper w-[90px]"
                >
                  {[
                    'https://picsum.photos/400/300',
                    'https://picsum.photos/400/300',
                    'https://picsum.photos/400/300',
                  ].map((item) => (
                    <Swiper.Item key={item}>
                      <Stack className="aspect-[2/3]">
                        <ImageArea src={item} />
                      </Stack>
                    </Swiper.Item>
                  ))}
                </Swiper>
              </Stack>
            </Stack>
            <Stack
              direction="horizontal"
              className="justify-between items-center"
            >
              <Stack gap={2}>
                <Stack gap={1}>
                  <Text className="text-label2 text-secondary font-semibold">
                    frame_name・ product_category
                  </Text>
                  <Text className="text-body2 line-clamp--1">product_name</Text>
                </Stack>
                <Text
                  className={
                    isOutOfStock
                      ? 'text-head2 text-disable'
                      : 'text-head2 text-strong'
                  }
                >
                  1.000KRW
                  {/* {formatPrice(
                    get(product, 'product_country_prices.0.product_price', 0),
                    get(product, 'product_country_prices.0.current_code', 'KRW')
                  )} */}
                </Text>
              </Stack>
              <Stack
                className="h-10 w-10 border-[1px solid] border-gray-100 rounded-full items-center justify-center cursor-pointer"
                // onClick={onShare}
              >
                <Image src={IconShare} className="h-4 w-4" />
              </Stack>
            </Stack>
          </Stack>
          <Stack className="py-6 px-4" gap={2}>
            <Text className="text-body2 font-semibold">설명</Text>
            <Text className="text-label1 text-quaternary w-full">
              product_description, product_description, product_description
            </Text>
          </Stack>
          <Stack
            gap={6}
            className={
              relevantProducts.length > 0
                ? 'display-flex py-6'
                : 'display-none py-6'
            }
          >
            <Text className="text-head2 font-semibold pl-4">연관 상품</Text>
            <ProductList
              layout={EProductListLayout.CAROUSEL}
              isDepthPage
              className="w-full ml-0"
              products={relevantProducts as unknown as SimpleProductItem[]}
            />
          </Stack>
          <Stack
            direction="horizontal"
            gap={2}
            className="border-t-[1px solid] border-gray-50 justify-center w-full p-4 max-w-[1200px] fixed bottom-0 bg-white z-10 h-20 box-border"
          >
            <Stack
              className="h-11 w-11 shrink-0 items-center justify-center cursor-pointer"
              // onClick={() => checkGuard(handleLikeUnlikeProduct)}
            >
              {Icon}
            </Stack>
            <BaseButton
              variant="outlined"
              size="mini"
              className="w-[]calc((100% - 44px - 16px) / 2)] h-12"
              // onClick={goToFindStore}
            >
              매장 찾기
            </BaseButton>
            <BaseButton
              variant="filled"
              size="mini"
              className="h-12 flex-grow-1"
              disabled={isOutOfStock || product?.product_sale_end}
              // onClick={() => checkGuard(handleClickAddProductToCart)}
            >
              {isOutOfStock ? '품절' : '장바구니 담기'}
            </BaseButton>
          </Stack>
        </Stack>
      </View>
    </DepthPageLayout>
  );
};

export default ProductPage;
