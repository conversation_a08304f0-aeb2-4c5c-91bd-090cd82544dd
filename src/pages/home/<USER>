import { useState } from 'react';
import HomeLayout from '@/components/layouts/HomeLayout';
import { Text, View } from '@tarojs/components';
// import "./index.scss";
import HomeTab from '@/components/home-page/HomeTab';
import Stack from '@/components/ui/stack';
import AboutUs from '@/components/common/footer/AboutUs';

export type THomeTabItem = {
  id: number | string;
  name: string;
  priority: number;
  is_default: boolean;
};

const MOCK_HOME_TABS: THomeTabItem[] = [
  { id: 1, name: 'Popular', priority: 1, is_default: true },
  { id: 2, name: 'New Arrivals', priority: 2, is_default: false },
  { id: 3, name: 'Featured', priority: 3, is_default: false },
];

const Home = () => {
  const [homeTabs, setHomeTabs] = useState<THomeTabItem[]>(MOCK_HOME_TABS);
  const [activeTabId, setActiveTabId] = useState<number | string>(0);

  // const { data, isLoading, error } = useQuery({
  //   queryKey: ['todos'],
  //   queryFn: () => apiPublic.get(`/todos`),
  // });
  //
  // console.log(data?.data, 'data');

  // async function fetchData() {
  //   try {
  //     const response = await apiPublic.get(`/todos`);
  //     // const response = await axios.get(
  //     //   "https://jsonplaceholder.typicode.com/todos"
  //     // );
  //     console.log(response.data, 'response.data');
  //   } catch (error) {
  //     console.error('Error:', error);
  //   }
  // }
  //
  // useEffect(() => {
  //   // fetchData();
  // }, []);

  return (
    <HomeLayout>
      <Stack direction="vertical" className="px-4 pb-2 bg-white flex-grow">
        <View className="flex h-10 gap-4 overflow-auto snap-x snap-mandatory [&::-webkit-scrollbar]:hidden">
          {homeTabs.map((item) => {
            const isActive = activeTabId === item.id;
            return (
              <Text
                className={`snap-start h-10 whitespace-nowrap leading-[40px] font-semibold border-b-2 ${
                  isActive
                    ? 'border-red-500 text-black'
                    : 'border-transparent text-gray-400'
                }`}
                key={item.id}
                onClick={() => setActiveTabId(item.id)}
              >
                {item.name}
              </Text>
            );
          })}
        </View>
        <HomeTab />
      </Stack>

      <AboutUs />
    </HomeLayout>
  );
};

export default Home;
