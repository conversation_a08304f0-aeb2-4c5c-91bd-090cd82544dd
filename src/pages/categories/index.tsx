import { View } from '@tarojs/components';
import MainPageLayout from '@/components/layouts/MainPageLayout';
import HeaderCustomProductIcon from '@/components/common/headerItem/HeaderCustomProductIcon';
import SearchProductIcon from '@/components/common/headerItem/SearchProductIcon';
import CartIcon from '@/components/common/headerItem/CartIcon';

const Categories = () => {
  return (
    <MainPageLayout
      title="카테고리"
      headerActions={
        <>
          <HeaderCustomProductIcon />
          <SearchProductIcon />
          <CartIcon />
        </>
      }
    >
      <View className="px-4">Categories</View>
    </MainPageLayout>
  );
};

export default Categories;
