import { View, Button } from '@tarojs/components';
import React, { useState } from 'react';
import Taro from '@tarojs/taro';
import HeaderSearchProduct from '@/components/search-product/HeaderSearchProduct';
import { useToastStore } from '@/stores/toastStore';

const SearchProduct = () => {
  const [searchValue, setSearchValue] = useState('');
  const statusBarHeight = Taro.getSystemInfoSync().statusBarHeight || 0;

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // TODO: Implement search logic here
  };

  const handleShowToast = () => {};

  return (
    <View className="flex flex-col min-h-screen bg-white">
      <HeaderSearchProduct
        searchValue={searchValue}
        onSearchChange={handleSearch}
      />

      <View
        className="flex-1 p-4"
        style={{ marginTop: `${statusBarHeight + 44}px` }}
      ></View>
    </View>
  );
};

export default SearchProduct;
