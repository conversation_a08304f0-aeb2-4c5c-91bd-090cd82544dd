import { View } from '@tarojs/components';
import React from 'react';
import Taro from '@tarojs/taro';
import HeaderSearchProduct from '@/components/search-product/HeaderSearchProduct';

const SearchProduct = () => {
  const statusBarHeight = Taro.getSystemInfoSync().statusBarHeight || 0;

  const handleSearch = (value: string) => {
    console.log('Search for:', value);
    // TODO: Implement search logic here
  };

  const handleSuggestionSelect = (item: any) => {
    console.log('Selected suggestion:', item);
    // TODO: Navigate to product/IP page
    Taro.navigateTo({
      url: `/pages/ip-page/index?id=${item.frame_id}`,
    });
  };

  return (
    <View className="flex flex-col min-h-screen bg-white">
      <HeaderSearchProduct
        onSearchChange={handleSearch}
        onSuggestionSelect={handleSuggestionSelect}
      />

      <View
        className="flex-1 p-4"
        style={{ marginTop: `${statusBarHeight + 48}px` }}
      ></View>
    </View>
  );
};

export default SearchProduct;
