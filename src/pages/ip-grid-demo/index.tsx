import React from 'react';
import { View } from '@tarojs/components';
import IpGrid from '../../components/ip/IpGrid';

const IpGridDemo: React.FC = () => {
  // Sample data for IpGrid
  const sampleData = [
    {
      frame_id: 1,
      frame_image_url: 'https://picsum.photos/200/200?random=1',
      frame_name: 'IP Category 1',
    },
    {
      frame_id: 2,
      frame_image_url: 'https://picsum.photos/200/200?random=2',
      frame_name: 'IP Category 2',
    },
    {
      frame_id: 3,
      frame_image_url: 'https://picsum.photos/200/200?random=3',
      frame_name: 'IP Category 3',
    },
    {
      frame_id: 4,
      frame_image_url: 'https://picsum.photos/200/200?random=4',
      frame_name: 'IP Category 4',
    },
    {
      frame_id: 5,
      frame_image_url: 'https://picsum.photos/200/200?random=5',
      frame_name: 'IP Category 5',
    },
    {
      frame_id: 6,
      frame_image_url: 'https://picsum.photos/200/200?random=6',
      frame_name: 'IP Category 6',
    },
    {
      frame_id: 7,
      frame_image_url: 'https://picsum.photos/200/200?random=7',
      frame_name: 'IP Category 7',
    },
    {
      frame_id: 8,
      frame_image_url: 'https://picsum.photos/200/200?random=8',
      frame_name: 'IP Category 8',
    },
    {
      frame_id: 9,
      frame_image_url: 'https://picsum.photos/200/200?random=9',
      frame_name: 'IP Category 9',
    },
    {
      frame_id: 10,
      frame_image_url: 'https://picsum.photos/200/200?random=10',
      frame_name: 'IP Category 10',
    },
    {
      frame_id: 11,
      frame_image_url: 'https://picsum.photos/200/200?random=11',
      frame_name: 'IP Category 11',
    },
    {
      frame_id: 12,
      frame_image_url: 'https://picsum.photos/200/200?random=12',
      frame_name: 'IP Category 12',
    },
  ];

  return (
    <View className="p-4">
      <View className="mb-8">
        <h2 className="text-lg font-bold mb-4">Grid with less than 10 items</h2>
        <IpGrid data={sampleData.slice(0, 8)} />
      </View>

      <View>
        <h2 className="text-lg font-bold mb-4">Grid with more than 10 items</h2>
        <IpGrid data={sampleData} />
      </View>
    </View>
  );
};

export default IpGridDemo;
