import { ICreateCustomProductRequest } from '@/types/imageEditor';

import {
  EPhotoCardLayout,
  EPhotoFace,
} from '@/constants/image-editor/photocard';
import {
  EImageColor,
  EProductPhotoType,
  initPhotoSlot,
} from '@/constants/image-editor/imageEditor';
import { EMode } from '@/constants/enums';
import { PhotoCardEditorProvider } from '@/contexts/PhotoCardController';
import CustomPhotoCardLayout from '@/components/custom-product/CustomPhotoCardLayout';

export const initPhotoCardEditor: ICreateCustomProductRequest = {
  product_type: EProductPhotoType.PHOTO_CARD,
  slots: [
    {
      position: EPhotoFace.FRONT,
      layout: EPhotoCardLayout.DEFAULT,
      color: EImageColor.BLACK,
      export_img_url: '',
      images: [{ ...initPhotoSlot }],
    },
    {
      position: EPhotoFace.BACK,
      layout: EPhotoCardLayout.DEFAULT,
      color: EImageColor.BLACK,
      export_img_url: '',
      images: [{ ...initPhotoSlot }],
    },
  ],
};

export default function PhotoCard() {
  return (
    <PhotoCardEditorProvider initData={initPhotoCardEditor} mode={EMode.CREATE}>
      <CustomPhotoCardLayout />
    </PhotoCardEditorProvider>
  );
}
