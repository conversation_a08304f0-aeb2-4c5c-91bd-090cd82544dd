// import SelectProductType from 'components/custom-product/SelectProductType';
// import RecommendLogin from 'components/custom-product/RecommendLogin';
// import { checkLoggedIn } from 'utils/auth';
import SelectProductType from '@/components/custom-product/SelectProductType';

const CreateCustomProduct = () => {
  // const isLoggedIn = checkLoggedIn();

  return (
    <>
      <SelectProductType />
      {/*{isLoggedIn ? null : <RecommendLogin />}*/}
    </>
  );
};

export default CreateCustomProduct;
