import { create } from 'zustand';

export type ToastType = 'success' | 'error' | 'loading' | 'info';

interface ToastState {
  visible: boolean;
  title: string;
  description?: string;
  type: ToastType;
  duration: number;
  actions?: React.ReactNode;
  showToast: (data: {
    title: string;
    description?: string;
    type?: ToastType;
    duration?: number;
    actions?: React.ReactNode;
  }) => void;
  hideToast: () => void;
}

export const useToastStore = create<ToastState>((set) => ({
  visible: false,
  title: '',
  description: '',
  type: 'info',
  duration: 3000,
  showToast: ({
    title,
    description,
    type = 'success',
    duration = 3000,
    actions,
  }) => {
    set({ visible: true, title, description, type, duration, actions });
    if (duration > 0) {
      setTimeout(() => {
        set({ visible: false });
      }, duration);
    }
  },
  hideToast: () =>
    set({
      visible: false,
      title: '',
      description: '',
      type: 'info',
      actions: null,
    }),
}));
