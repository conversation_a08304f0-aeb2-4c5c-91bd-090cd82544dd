import { create } from 'zustand';

interface LoadingState {
  isLoading: boolean;
  startLoading: () => void;
  stopLoading: () => void;
}

export const useLoadingStore = create<LoadingState>((set) => ({
  isLoading: false,

  startLoading: () => {
    set({
      isLoading: true,
    });
  },

  stopLoading: () => {
    setTimeout(() => {
      set({
        isLoading: false,
      });
    }, 100);
  },
}));
